#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel Import Tab for Employee Salary Management System
Handles importing data from Excel files
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import datetime

class ExcelImportTab:
    """Tab for importing data from Excel files"""
    
    def __init__(self, parent, db_manager):
        """Initialize the Excel import tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create form elements
        self.create_import_form()
        
        # Initialize variables
        self.excel_data = None
        self.file_path = None
        
    def create_import_form(self):
        """Create the Excel import form"""
        # Main container
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="اختيار ملف Excel")
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # File path
        ttk.Label(file_frame, text="مسار الملف:").grid(row=0, column=2, sticky=tk.E, padx=5, pady=5)
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50, state="readonly").grid(
            row=0, column=1, sticky=tk.W, padx=5, pady=5
        )
        
        # Browse button
        ttk.Button(
            file_frame, 
            text="استعراض...", 
            command=self.browse_file
        ).grid(row=0, column=0, padx=5, pady=5)
        
        # Department selection
        ttk.Label(file_frame, text="المجموعة:").grid(row=1, column=2, sticky=tk.E, padx=5, pady=5)
        self.department_var = tk.StringVar()
        self.department_combo = ttk.Combobox(file_frame, textvariable=self.department_var, width=30)
        self.department_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.department_combo.bind("<<ComboboxSelected>>", self.on_department_selected)

        # New department entry (initially hidden)
        self.new_department_var = tk.StringVar()
        self.new_department_entry = ttk.Entry(file_frame, textvariable=self.new_department_var, width=30)

        # Load departments
        self.load_departments()
        
        # Year and month
        ttk.Label(file_frame, text="السنة:").grid(row=2, column=2, sticky=tk.E, padx=5, pady=5)
        self.year_var = tk.StringVar()
        current_year = datetime.datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 6)]
        self.year_combo = ttk.Combobox(file_frame, textvariable=self.year_var, values=years, width=10)
        self.year_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.year_var.set(str(current_year))
        
        ttk.Label(file_frame, text="الشهر:").grid(row=3, column=2, sticky=tk.E, padx=5, pady=5)
        self.month_var = tk.StringVar()
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        self.month_combo = ttk.Combobox(file_frame, textvariable=self.month_var, values=months, width=15)
        self.month_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        current_month = datetime.datetime.now().month
        self.month_var.set(months[current_month - 1])
        
        # Load button
        ttk.Button(
            file_frame, 
            text="تحميل البيانات", 
            command=self.load_excel_data
        ).grid(row=4, column=0, columnspan=3, padx=5, pady=10)
        
        # Preview section
        preview_frame = ttk.LabelFrame(main_frame, text="معاينة البيانات")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview for data preview
        self.create_preview_treeview(preview_frame)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)

        # Import button
        ttk.Button(
            buttons_frame,
            text="استيراد البيانات",
            command=self.import_data
        ).pack(side=tk.RIGHT, padx=5)

        # Return to main button
        ttk.Button(
            buttons_frame,
            text="العودة للواجهة الرئيسية",
            command=self.return_to_main,
            style="Blue.TButton"
        ).pack(side=tk.LEFT, padx=5)

    def return_to_main(self):
        """Return to main interface by switching to the first tab"""
        # Get the parent notebook and switch to the first tab
        parent_notebook = self.frame.master
        if hasattr(parent_notebook, 'select'):
            parent_notebook.select(0)  # Select the first tab

    def create_preview_treeview(self, parent):
        """Create a treeview to preview Excel data"""
        # Create a frame for the treeview and scrollbars
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical")
        vsb.pack(side=tk.LEFT, fill=tk.Y)  # Changed from RIGHT to LEFT for RTL
        
        # Create horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal")
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Create treeview with reversed column order (RTL)
        self.preview_tree = ttk.Treeview(
            tree_frame,
            columns=("alsafi", "prim_salary", "job_title", "name"),  # Reversed order
            show="headings",
            yscrollcommand=vsb.set,
            xscrollcommand=hsb.set
        )
        
        # Configure scrollbars
        vsb.config(command=self.preview_tree.yview)
        hsb.config(command=self.preview_tree.xview)
        
        # Configure columns in RTL order
        self.preview_tree.heading("alsafi", text="الصافي")
        self.preview_tree.heading("prim_salary", text="الراتب الاسمي")
        self.preview_tree.heading("job_title", text="العنوان الوظيفي")
        self.preview_tree.heading("name", text="اسم الموظف")
        
        # Set column widths and anchors for RTL display
        self.preview_tree.column("alsafi", width=100, anchor=tk.W)  # Right-aligned in RTL
        self.preview_tree.column("prim_salary", width=100, anchor=tk.W)  # Right-aligned in RTL
        self.preview_tree.column("job_title", width=150, anchor=tk.E)  # Left-aligned in RTL
        self.preview_tree.column("name", width=200, anchor=tk.E)  # Left-aligned in RTL
        
        self.preview_tree.pack(fill=tk.BOTH, expand=True)
        
    def load_departments(self):
        """Load departments into the combobox"""
        departments = self.db_manager.get_departments()
        department_names = [dept["name"] for dept in departments]
        department_names.append("إضافة مجموعة جديدة...")
        self.department_combo["values"] = department_names

        if department_names:
            self.department_var.set(department_names[0])

    def on_department_selected(self, event):
        """Handle department selection"""
        selected = self.department_var.get()

        if selected == "إضافة مجموعة جديدة...":
            # Show entry field for new department
            self.new_department_entry.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(self.new_department_entry.master, text="اسم المجموعة الجديدة:").grid(row=2, column=2, sticky=tk.E, padx=5, pady=5)

            # Add button to save new department
            save_dept_btn = ttk.Button(
                self.new_department_entry.master,
                text="حفظ المجموعة",
                command=self.save_new_department
            )
            save_dept_btn.grid(row=2, column=0, padx=5, pady=5)
            self.save_dept_btn = save_dept_btn
        else:
            # Hide entry field
            self.new_department_entry.grid_remove()
            if hasattr(self, 'save_dept_btn'):
                self.save_dept_btn.grid_remove()

    def save_new_department(self):
        """Save new department to database"""
        new_dept_name = self.new_department_var.get().strip()

        if not new_dept_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المجموعة")
            return

        try:
            # Check if department already exists
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept["name"].lower() == new_dept_name.lower():
                    messagebox.showerror("خطأ", "هذه المجموعة موجودة بالفعل")
                    return

            # Add new department
            dept_id = self.db_manager.add_department(new_dept_name)

            # Reload departments
            self.load_departments()

            # Select the new department
            self.department_var.set(new_dept_name)

            # Hide entry field
            self.new_department_entry.grid_remove()
            self.save_dept_btn.grid_remove()

            # Clear entry
            self.new_department_var.set("")

            messagebox.showinfo("نجح", f"تم إضافة المجموعة '{new_dept_name}' بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المجموعة:\n{str(e)}")
            
    def browse_file(self):
        """Open file dialog to select Excel file"""
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("جميع ملفات Excel", "*.xlsx *.xls *.xlsm *.xlsb *.xltx *.xltm *.xlt *.xml *.xlam *.xla *.xlw *.xlr"),
                ("Excel 2007-2021 (*.xlsx)", "*.xlsx"),
                ("Excel 97-2003 (*.xls)", "*.xls"),
                ("Excel with Macros (*.xlsm)", "*.xlsm"),
                ("Excel Binary (*.xlsb)", "*.xlsb"),
                ("Excel Template (*.xltx, *.xltm, *.xlt)", "*.xltx *.xltm *.xlt"),
                ("Excel Add-In (*.xlam, *.xla)", "*.xlam *.xla"),
                ("جميع الملفات", "*.*")
            ],
            title="اختر ملف Excel"
        )
        
        if file_path:
            self.file_path = file_path
            self.file_path_var.set(file_path)
            
    def load_excel_data(self):
        """Load data from the selected Excel file"""
        if not self.file_path:
            messagebox.showerror("خطأ", "يرجى اختيار ملف Excel أولاً")
            return
            
        try:
            # Check if required dependencies are installed
            try:
                import xlrd
            except ImportError:
                # Install xlrd if missing
                import subprocess
                import sys
                
                messagebox.showinfo(
                    "تثبيت المكتبات", 
                    "جاري تثبيت المكتبات المطلوبة لقراءة ملفات Excel...\nيرجى الانتظار."
                )
                
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "xlrd>=1.0.0"])
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl>=3.0.0"])
                    messagebox.showinfo("تثبيت المكتبات", "تم تثبيت المكتبات بنجاح. جاري قراءة الملف...")
                except Exception as e:
                    messagebox.showerror(
                        "خطأ", 
                        f"فشل تثبيت المكتبات المطلوبة:\n{str(e)}\n\n"
                        "يرجى تثبيت المكتبات يدوياً باستخدام الأمر:\n"
                        "pip install xlrd>=1.0.0 openpyxl>=3.0.0"
                    )
                    return
            
            # Determine file extension
            file_ext = os.path.splitext(self.file_path)[1].lower()
            
            try:
                # محاولة قراءة الملف باستخدام المحرك المناسب
                if file_ext == '.xls':
                    # For older Excel files (.xls)
                    df = pd.read_excel(self.file_path, engine='xlrd')
                elif file_ext in ['.xlsx', '.xlsm', '.xlsb', '.xltx', '.xltm', '.xlt']:
                    # For newer Excel files
                    df = pd.read_excel(self.file_path, engine='openpyxl')
                elif file_ext == '.csv':
                    # For CSV files
                    df = pd.read_csv(self.file_path, encoding='utf-8')
                else:
                    # محاولة قراءة الملف بغض النظر عن الامتداد
                    try:
                        df = pd.read_excel(self.file_path, engine='openpyxl')
                    except:
                        try:
                            df = pd.read_excel(self.file_path, engine='xlrd')
                        except:
                            try:
                                df = pd.read_csv(self.file_path, encoding='utf-8')
                            except:
                                raise Exception(f"لم يتم التعرف على تنسيق الملف: {file_ext}")
            except Exception as e:
                messagebox.showerror(
                    "خطأ في قراءة الملف", 
                    f"لم يتم التمكن من قراءة الملف: {str(e)}\n\n"
                    "تأكد من أن الملف هو ملف Excel صالح وأنه غير مفتوح في برنامج آخر."
                )
                return
            
            # تحسين التعامل مع أسماء الأعمدة المختلفة
            # قائمة بالأسماء البديلة المحتملة لكل عمود مطلوب
            column_alternatives = {
                "EmployName": ["EmployName", "Name", "EmployeeName", "Employee Name", "Full Name", "اسم_الموظف", "الاسم", "اسم الموظف"],
                "JopTitle": ["JopTitle", "JobTitle", "JobTaitle", "Job Title", "Title", "Position", "العنوان_الوظيفي", "الوظيفة", "المنصب", "العنوان الوظيفي"]
            }
            
            # قائمة بالأعمدة الإضافية المفيدة (غير إلزامية)
            optional_columns = {
                "PrimSalaryC": ["PrimSalaryC", "PrimarySalary", "Salary", "BaseSalary", "Base Salary", "الراتب_الاسمي", "الراتب", "الراتب الاسمي"],
                "Alsafi": ["Alsafi", "NetSalary", "Net", "NetPay", "Net Pay", "الصافي", "صافي_الراتب", "صافي الراتب"],
                "Tin": ["Tin", "TotalIn", "Total In", "TotalEntitlements", "Total Entitlements", "مجموع_الاستحقاقات", "مجموع الاستحقاقات"],
                "Tout": ["Tout", "TotalOut", "Total Out", "TotalDeductions", "Total Deductions", "مجموع_الاستقطاعات", "مجموع الاستقطاعات"],
                "department": ["department", "Department", "dept", "Dept", "DepartmentName", "المجموعة", "القسم", "الدائرة", "اسم_المجموعة", "اسم المجموعة"]
            }
            
            # تعيين الأعمدة الفعلية في الملف
            actual_columns = {}
            missing_columns = []
            
            # عرض أسماء الأعمدة الموجودة في الملف للتشخيص
            print("أسماء الأعمدة الموجودة في الملف:")
            for col in df.columns:
                print(f"- {col}")
            
            # البحث عن الأعمدة المطلوبة باستخدام الأسماء البديلة
            for required_col, alternatives in column_alternatives.items():
                found = False
                
                # البحث المباشر
                for alt in alternatives:
                    if alt in df.columns:
                        actual_columns[required_col] = alt
                        found = True
                        break
                
                # البحث غير الحساس لحالة الأحرف
                if not found:
                    for col in df.columns:
                        for alt in alternatives:
                            if col.lower() == alt.lower():
                                actual_columns[required_col] = col
                                found = True
                                break
                        if found:
                            break
                
                # البحث الجزئي
                if not found:
                    for col in df.columns:
                        for alt in alternatives:
                            if alt.lower() in col.lower() or col.lower() in alt.lower():
                                actual_columns[required_col] = col
                                found = True
                                break
                        if found:
                            break
                
                if not found:
                    missing_columns.append(required_col)
            
            # البحث عن الأعمدة الاختيارية
            for optional_col, alternatives in optional_columns.items():
                found = False
                
                # البحث المباشر
                for alt in alternatives:
                    if alt in df.columns:
                        actual_columns[optional_col] = alt
                        found = True
                        break
                
                # البحث غير الحساس لحالة الأحرف
                if not found:
                    for col in df.columns:
                        for alt in alternatives:
                            if col.lower() == alt.lower():
                                actual_columns[optional_col] = col
                                found = True
                                break
                        if found:
                            break
                
                # البحث الجزئي
                if not found:
                    for col in df.columns:
                        for alt in alternatives:
                            if alt.lower() in col.lower() or col.lower() in alt.lower():
                                actual_columns[optional_col] = col
                                found = True
                                break
                        if found:
                            break
            
            # إذا كانت هناك أعمدة مفقودة، عرض رسالة خطأ
            if missing_columns:
                messagebox.showerror(
                    "خطأ", 
                    f"الأعمدة التالية مفقودة في الملف: {', '.join(missing_columns)}\n\n"
                    f"الأعمدة المطلوبة هي:\n"
                    f"- اسم الموظف (EmployName أو Name أو اسم الموظف)\n"
                    f"- العنوان الوظيفي (JopTitle أو JobTitle أو العنوان الوظيفي)\n\n"
                    f"أسماء الأعمدة الموجودة في الملف:\n{', '.join(df.columns)}"
                )
                return
                
            # تخزين أسماء الأعمدة الفعلية للاستخدام لاحقًا
            self.actual_columns = actual_columns
            
            # عرض الأعمدة التي تم العثور عليها للتشخيص
            print("الأعمدة التي تم العثور عليها:")
            for key, value in actual_columns.items():
                print(f"- {key}: {value}")
            
            # الحصول على المجموعة المختارة
            selected_department = self.department_var.get()

            # التحقق مما إذا كان عمود المجموعة موجوداً في الملف
            if "department" in actual_columns:
                department_column = actual_columns["department"]

                # تصفية البيانات بناءً على المجموعة المختارة
                if (selected_department and
                    selected_department != "جميع المجموعات" and
                    selected_department != "إضافة مجموعة جديدة..."):

                    # البحث عن الصفوف التي تحتوي على المجموعة المختارة (بحث دقيق)
                    filtered_df = df[df[department_column].astype(str).str.strip() == selected_department]

                    if len(filtered_df) == 0:
                        # محاولة البحث الجزئي إذا لم يتم العثور على تطابق دقيق
                        filtered_df = df[df[department_column].astype(str).str.contains(selected_department, case=False, na=False)]

                        if len(filtered_df) == 0:
                            # إذا لم يتم العثور على سجلات، عرض رسالة تحذير
                            messagebox.showwarning(
                                "تحذير",
                                f"لم يتم العثور على سجلات للمجموعة '{selected_department}' في الملف.\n"
                                f"عمود المجموعة: {department_column}\n"
                                f"القيم الموجودة: {list(df[department_column].unique())}\n"
                                "سيتم عرض جميع السجلات."
                            )
                            self.excel_data = df
                        else:
                            # تخزين البيانات المصفاة (البحث الجزئي)
                            self.excel_data = filtered_df
                            messagebox.showinfo(
                                "تصفية البيانات",
                                f"تم تصفية البيانات للمجموعة '{selected_department}' (بحث جزئي).\n"
                                f"تم العثور على {len(filtered_df)} سجل."
                            )
                    else:
                        # تخزين البيانات المصفاة (البحث الدقيق)
                        self.excel_data = filtered_df
                        messagebox.showinfo(
                            "تصفية البيانات",
                            f"تم تصفية البيانات للمجموعة '{selected_department}'.\n"
                            f"تم العثور على {len(filtered_df)} سجل."
                        )
                else:
                    # إذا لم يتم اختيار مجموعة، عرض جميع السجلات
                    self.excel_data = df
            else:
                # إذا لم يكن عمود المجموعة موجوداً، عرض جميع السجلات
                self.excel_data = df
                if (selected_department and
                    selected_department != "جميع المجموعات" and
                    selected_department != "إضافة مجموعة جديدة..."):
                    messagebox.showwarning(
                        "تحذير",
                        f"لم يتم العثور على عمود المجموعة في الملف.\n"
                        f"الأعمدة الموجودة: {list(df.columns)}\n"
                        "سيتم عرض جميع السجلات."
                    )
            
            # Clear existing items in treeview
            for item in self.preview_tree.get_children():
                self.preview_tree.delete(item)
                
            # Add data to treeview (in RTL order)
            for _, row in df.iterrows():
                # استخدام أسماء الأعمدة الفعلية من الملف
                employ_name = row.get(self.actual_columns.get("EmployName", ""), "")
                job_title = row.get(self.actual_columns.get("JopTitle", ""), "")
                
                # استخدام الأعمدة الاختيارية إذا تم العثور عليها
                prim_salary = row.get(self.actual_columns.get("PrimSalaryC", ""), 0)
                if prim_salary == 0 or prim_salary == "":
                    prim_salary = 0
                    
                alsafi = row.get(self.actual_columns.get("Alsafi", ""), 0)
                if alsafi == 0 or alsafi == "":
                    alsafi = 0
                
                self.preview_tree.insert(
                    "", 
                    tk.END, 
                    values=(
                        alsafi,
                        prim_salary,
                        job_title,
                        employ_name
                    )
                )
                
            messagebox.showinfo("تحميل البيانات", f"تم تحميل {len(df)} سجل من الملف")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء قراءة الملف:\n{str(e)}")
            
    def import_data(self):
        """Import data from Excel to the database"""
        if self.excel_data is None:
            messagebox.showerror("خطأ", "يرجى تحميل بيانات من ملف Excel أولاً")
            return
            
        try:
            # Get department
            department_name = self.department_var.get()
            if not department_name:
                messagebox.showerror("خطأ", "يرجى اختيار المجموعة")
                return
                
            # Get department ID
            departments = self.db_manager.get_departments()
            department_id = None
            for dept in departments:
                if dept["name"] == department_name:
                    department_id = dept["id"]
                    break
                    
            if not department_id:
                messagebox.showerror("خطأ", "المجموعة غير موجودة")
                return
                
            # Get year and month
            try:
                year = int(self.year_var.get())
                
                months = [
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                ]
                month = months.index(self.month_var.get()) + 1
            except:
                messagebox.showerror("خطأ", "يرجى اختيار السنة والشهر بشكل صحيح")
                return
                
            # Start import
            imported_count = 0
            updated_count = 0
            error_count = 0
            
            # Column mapping
            column_mapping = {
                "EmployName": "name",
                "JopTitle": "job_title",  # تصحيح اسم العمود من JobTitle إلى JopTitle
                "PrimSalaryC": "prim_salary_c",
                "Alsafi": "alsafi",
                "Tout": "tout",
                "Tin": "tin",
                "MansibC": "mansib_c",
                "shahada": "shahada",
                "zawjiya": "zawjiya",
                "athfal": "athfal",
                "denger": "denger",
                "nakil": "nakil",
                "handasiya": "handasiya",
                "Arzaak": "arzaak",
                "IncomGC": "incom_gc",
                "tokifat": "tokifat",
                "eskan": "eskan",
                "dariba": "dariba",
                "eikari": "eikari",
                "reaya": "reaya",
                "raseem": "raseem",
                "tanfeeth": "tanfeeth",
                "rafidaen": "rafidaen",
                "Rasheed": "rasheed",
                "AmanatOkra": "amanat_okra",
                "Note": "note",
                "Eijar": "eijar"
            }
            
            # Process each row
            for _, row in self.excel_data.iterrows():
                try:
                    # Get employee name and job title using actual column names
                    name = row.get(self.actual_columns.get("EmployName", ""), "").strip()
                    job_title = row.get(self.actual_columns.get("JopTitle", ""), "").strip()
                    
                    if not name:
                        error_count += 1
                        continue
                        
                    # Check if employee exists
                    employees = self.db_manager.get_employees(department_id)
                    employee_id = None
                    
                    for emp in employees:
                        if emp["name"] == name:
                            employee_id = emp["id"]
                            break
                            
                    # Create new employee if not exists
                    if not employee_id:
                        employee_id = self.db_manager.add_employee(name, job_title, department_id)
                        imported_count += 1
                    else:
                        updated_count += 1
                        
                    # Prepare salary data
                    salary_data = {}
                    
                    # Map Excel columns to database fields using actual column names
                    for excel_col, db_field in column_mapping.items():
                        # استخدام الاسم الفعلي للعمود إذا كان موجوداً في قائمة الأعمدة الفعلية
                        actual_col = self.actual_columns.get(excel_col, excel_col)
                        if actual_col in row and not pd.isna(row[actual_col]):
                            salary_data[db_field] = row[actual_col]
                            
                    # Save salary record
                    self.db_manager.add_salary_record(employee_id, year, month, salary_data)
                    
                except Exception as e:
                    print(f"Error importing row: {str(e)}")
                    error_count += 1
                    
            # Show results
            messagebox.showinfo(
                "استيراد البيانات", 
                f"تم استيراد {imported_count} موظف جديد\n"
                f"تم تحديث {updated_count} موظف موجود\n"
                f"فشل استيراد {error_count} سجل"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}")
            
    def save_data(self):
        """Save imported data"""
        # This method is called from the main window
        # The actual saving happens in the import_data method
        pass