#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Filter Tab for Employee Salary Management System
Allows filtering and searching through salary records
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class FilterTab:
    """Tab for filtering and searching salary records"""
    
    def __init__(self, parent, db_manager):
        """Initialize the filter tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create form elements
        self.create_filter_form()
        
    def create_filter_form(self):
        """Create the filter form"""
        # Main container with two frames
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top frame for filters
        top_frame = ttk.LabelFrame(main_frame, text="معايير التصفية")
        top_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Bottom frame for results
        bottom_frame = ttk.LabelFrame(main_frame, text="نتائج البحث")
        bottom_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create filter controls
        self.create_filter_controls(top_frame)
        
        # Create results display
        self.create_results_display(bottom_frame)
        
        # Create summary display instead of chart
        self.create_summary_display(main_frame)
        
    def create_filter_controls(self, parent):
        """Create filter controls"""
        # Create a grid layout for filters
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Employee name filter
        ttk.Label(filter_frame, text="اسم الموظف:").grid(row=0, column=3, sticky=tk.E, padx=5, pady=5)
        self.employee_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(filter_frame, textvariable=self.employee_var, width=25)
        self.employee_combo.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Department filter
        ttk.Label(filter_frame, text="المجموعة:").grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.department_var = tk.StringVar()
        self.department_combo = ttk.Combobox(filter_frame, textvariable=self.department_var, width=25)
        self.department_combo.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Job title filter
        ttk.Label(filter_frame, text="العنوان الوظيفي:").grid(row=1, column=3, sticky=tk.E, padx=5, pady=5)
        self.job_title_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.job_title_var, width=25).grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Year filter
        ttk.Label(filter_frame, text="السنة:").grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.year_var = tk.StringVar()
        current_year = datetime.datetime.now().year
        years = ["الكل"] + [str(year) for year in range(current_year - 5, current_year + 6)]
        self.year_combo = ttk.Combobox(filter_frame, textvariable=self.year_var, values=years, width=10)
        self.year_combo.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.year_var.set("الكل")
        
        # Month filter
        ttk.Label(filter_frame, text="الشهر:").grid(row=2, column=3, sticky=tk.E, padx=5, pady=5)
        self.month_var = tk.StringVar()
        months = ["الكل"] + [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        self.month_combo = ttk.Combobox(filter_frame, textvariable=self.month_var, values=months, width=15)
        self.month_combo.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.month_var.set("الكل")
        
        # Salary range filter
        ttk.Label(filter_frame, text="الصافي من:").grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.min_salary_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.min_salary_var, width=10).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="الصافي إلى:").grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        self.max_salary_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.max_salary_var, width=10).grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Buttons frame
        buttons_frame = ttk.Frame(filter_frame)
        buttons_frame.grid(row=3, column=0, columnspan=4, padx=5, pady=10, sticky=tk.EW)

        # Search button
        ttk.Button(
            buttons_frame,
            text="بحث",
            command=self.search_records
        ).pack(side=tk.RIGHT, padx=5)

        # Return to main button
        ttk.Button(
            buttons_frame,
            text="العودة للواجهة الرئيسية",
            command=self.return_to_main,
            style="Blue.TButton"
        ).pack(side=tk.LEFT, padx=5)
        
        # Load departments and employees
        self.load_departments()
        self.load_employees()

    def return_to_main(self):
        """Return to main interface by switching to the first tab"""
        # Get the parent notebook and switch to the first tab
        parent_notebook = self.frame.master
        if hasattr(parent_notebook, 'select'):
            parent_notebook.select(0)  # Select the first tab
        
    def create_results_display(self, parent):
        """Create the results display area"""
        # Create a frame for the treeview
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create scrollbars
        vsb = ttk.Scrollbar(tree_frame, orient="vertical")
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal")
        
        # Create treeview
        self.results_tree = ttk.Treeview(
            tree_frame,
            yscrollcommand=vsb.set,
            xscrollcommand=hsb.set
        )
        
        # Configure scrollbars
        vsb.config(command=self.results_tree.yview)
        hsb.config(command=self.results_tree.xview)
        
        # Place scrollbars
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Configure treeview columns
        self.results_tree["columns"] = (
            "employee_name", "job_title", "department", "year", "month", 
            "prim_salary", "tin", "tout", "alsafi"
        )
        
        self.results_tree.column("#0", width=0, stretch=tk.NO)
        self.results_tree.column("employee_name", width=150, anchor=tk.W)
        self.results_tree.column("job_title", width=150, anchor=tk.W)
        self.results_tree.column("department", width=150, anchor=tk.W)
        self.results_tree.column("year", width=70, anchor=tk.CENTER)
        self.results_tree.column("month", width=100, anchor=tk.W)
        self.results_tree.column("prim_salary", width=100, anchor=tk.E)
        self.results_tree.column("tin", width=100, anchor=tk.E)
        self.results_tree.column("tout", width=100, anchor=tk.E)
        self.results_tree.column("alsafi", width=100, anchor=tk.E)
        
        # Configure headings
        self.results_tree.heading("employee_name", text="اسم الموظف")
        self.results_tree.heading("job_title", text="العنوان الوظيفي")
        self.results_tree.heading("department", text="المجموعة")
        self.results_tree.heading("year", text="السنة")
        self.results_tree.heading("month", text="الشهر")
        self.results_tree.heading("prim_salary", text="الراتب الاسمي")
        self.results_tree.heading("tin", text="الاستحقاقات")
        self.results_tree.heading("tout", text="الاستقطاعات")
        self.results_tree.heading("alsafi", text="الصافي")
        
        # Pack treeview
        self.results_tree.pack(fill=tk.BOTH, expand=True)
        
        # Bind double-click event
        self.results_tree.bind("<Double-1>", self.on_record_double_click)
        
    def create_summary_display(self, parent):
        """Create a summary display area instead of chart"""
        # Create a frame for the summary
        self.summary_frame = ttk.LabelFrame(parent, text="ملخص النتائج")
        self.summary_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Add initial message
        self.summary_label = ttk.Label(
            self.summary_frame,
            text="استخدم معايير التصفية أعلاه ثم اضغط على زر 'بحث' لعرض النتائج",
            wraplength=600,
            justify=tk.CENTER,
            padding=20
        )
        self.summary_label.pack(fill=tk.X)
        
    def load_departments(self):
        """Load departments into the combobox"""
        departments = self.db_manager.get_departments()
        department_names = ["الكل"] + [dept["name"] for dept in departments]
        self.department_combo["values"] = department_names
        self.department_var.set("الكل")
        
    def load_employees(self):
        """Load employees into the combobox"""
        employees = self.db_manager.get_employees()
        employee_names = ["الكل"] + [emp["name"] for emp in employees]
        self.employee_combo["values"] = employee_names
        self.employee_var.set("الكل")
        
        # Store employee data for lookup
        self.employee_data = {emp["name"]: emp for emp in employees}
        
    def search_records(self):
        """Search for records based on filter criteria"""
        try:
            # Build filter criteria
            filters = {}
            
            # Employee filter
            if self.employee_var.get() != "الكل" and self.employee_var.get() in self.employee_data:
                filters["employee_id"] = self.employee_data[self.employee_var.get()]["id"]
                
            # Department filter
            if self.department_var.get() != "الكل":
                departments = self.db_manager.get_departments()
                for dept in departments:
                    if dept["name"] == self.department_var.get():
                        filters["department_id"] = dept["id"]
                        break
                        
            # Job title filter
            if self.job_title_var.get():
                filters["job_title"] = self.job_title_var.get()
                
            # Year filter
            if self.year_var.get() != "الكل":
                try:
                    filters["year"] = int(self.year_var.get())
                except:
                    pass
                    
            # Month filter
            if self.month_var.get() != "الكل":
                months = [
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                ]
                try:
                    month_idx = months.index(self.month_var.get()) + 1
                    filters["month"] = month_idx
                except:
                    pass
                    
            # Search for records
            records = self.db_manager.search_salary_records(filters)
            
            # Apply salary range filter (client-side)
            if self.min_salary_var.get():
                try:
                    min_salary = float(self.min_salary_var.get())
                    records = [r for r in records if r["alsafi"] >= min_salary]
                except:
                    pass
                    
            if self.max_salary_var.get():
                try:
                    max_salary = float(self.max_salary_var.get())
                    records = [r for r in records if r["alsafi"] <= max_salary]
                except:
                    pass
                    
            # Clear existing items in treeview
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
                
            # Arabic month names
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            
            # Add records to treeview
            for record in records:
                month_idx = record["month"] - 1
                month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
                
                self.results_tree.insert(
                    "", 
                    tk.END, 
                    values=(
                        record["employee_name"],
                        record["job_title"],
                        record["department_name"],
                        record["year"],
                        month_name,
                        record["prim_salary_c"],
                        record["tin"],
                        record["tout"],
                        record["alsafi"]
                    ),
                    tags=("record",),
                    iid=record["id"]  # Use record ID as item ID for lookup
                )
                
            # Update summary if there are results
            self.update_summary(records)
                
            # Show result count
            messagebox.showinfo("نتائج البحث", f"تم العثور على {len(records)} سجل")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث:\n{str(e)}")
            
    def on_record_double_click(self, event):
        """Handle double-click on a record"""
        # Get the selected item
        item = self.results_tree.selection()[0]
        record_id = item
        
        # Show record details
        self.show_record_details(record_id)
        
    def show_record_details(self, record_id):
        """Show details for a specific record"""
        try:
            # Get the record from the database
            # We need to query the database directly since we only have the ID
            self.db_manager.cursor.execute(
                """SELECT sr.*, e.name as employee_name, e.job_title, d.name as department_name
                FROM salary_records sr
                JOIN employees e ON sr.employee_id = e.id
                JOIN departments d ON e.department_id = d.id
                WHERE sr.id = ?""",
                (record_id,)
            )
            record = self.db_manager.cursor.fetchone()
            
            if not record:
                messagebox.showerror("خطأ", "لم يتم العثور على السجل")
                return
                
            # Create a new window to display details
            details_window = tk.Toplevel(self.frame)
            details_window.title(f"تفاصيل راتب: {record['employee_name']}")
            details_window.geometry("600x500")
            details_window.resizable(True, True)
            
            # Create a frame for the details
            details_frame = ttk.Frame(details_window, padding=10)
            details_frame.pack(fill=tk.BOTH, expand=True)
            
            # Employee info section
            info_frame = ttk.LabelFrame(details_frame, text="معلومات الموظف")
            info_frame.pack(fill=tk.X, padx=5, pady=5)
            
            ttk.Label(info_frame, text=f"الاسم: {record['employee_name']}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(info_frame, text=f"العنوان الوظيفي: {record['job_title']}").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(info_frame, text=f"المجموعة: {record['department_name']}").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
            
            # Arabic month names
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            month_name = months[record["month"] - 1] if 1 <= record["month"] <= 12 else str(record["month"])
            
            ttk.Label(info_frame, text=f"الفترة: {month_name} {record['year']}").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
            
            # Create two columns for entitlements and deductions
            columns_frame = ttk.Frame(details_frame)
            columns_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # Entitlements column
            entitlements_frame = ttk.LabelFrame(columns_frame, text="الاستحقاقات")
            entitlements_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            entitlement_fields = [
                ("الراتب الاسمي", "prim_salary_c"),
                ("م. الزوجية", "zawjiya"),
                ("م. الأطفال", "athfal"),
                ("م. الخطورة", "denger"),
                ("المنصب", "mansib_c"),
                ("م. الشهادة", "shahada"),
                ("م. النقل", "nakil"),
                ("م. الهندسية", "handasiya"),
                ("م. المهنية", "arzaak"),
                ("م. الخدمة الجامعية", "incom_gc"),
                ("المجموع", "tin")
            ]
            
            for i, (label, field) in enumerate(entitlement_fields):
                ttk.Label(entitlements_frame, text=f"{label}:").grid(row=i, column=1, sticky=tk.E, padx=5, pady=2)
                ttk.Label(entitlements_frame, text=f"{record[field]:.2f}").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
                
            # Deductions column
            deductions_frame = ttk.LabelFrame(columns_frame, text="الاستقطاعات")
            deductions_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            deduction_fields = [
                ("التوقفات التقاعدية", "tokifat"),
                ("الضريبة", "dariba"),
                ("العقار", "eikari"),
                ("الرعاية الاجتماعية", "reaya"),
                ("رسم الطابع", "raseem"),
                ("التنفيذ", "tanfeeth"),
                ("الإسكان", "eskan"),
                ("الرشيد", "rasheed"),
                ("الرافدين", "rafidaen"),
                ("الإيجار", "eijar"),
                ("أمانات أخرى", "amanat_okra"),
                ("المجموع", "tout")
            ]
            
            for i, (label, field) in enumerate(deduction_fields):
                ttk.Label(deductions_frame, text=f"{label}:").grid(row=i, column=1, sticky=tk.E, padx=5, pady=2)
                ttk.Label(deductions_frame, text=f"{record[field]:.2f}").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
                
            # Net salary
            net_frame = ttk.Frame(details_frame)
            net_frame.pack(fill=tk.X, padx=5, pady=5)
            
            ttk.Label(net_frame, text="الصافي المدفوع:", font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=5)
            ttk.Label(net_frame, text=f"{record['alsafi']:.2f}", font=("Arial", 12, "bold"), foreground="green").pack(side=tk.LEFT, padx=5)
            
            # Notes
            if record["note"]:
                notes_frame = ttk.LabelFrame(details_frame, text="ملاحظات")
                notes_frame.pack(fill=tk.X, padx=5, pady=5)
                
                note_text = tk.Text(notes_frame, height=3, wrap=tk.WORD)
                note_text.pack(fill=tk.X, padx=5, pady=5)
                note_text.insert(tk.END, record["note"])
                note_text.config(state=tk.DISABLED)
                
            # Close button
            ttk.Button(
                details_frame, 
                text="إغلاق", 
                command=details_window.destroy
            ).pack(pady=10)
            
            # Make window modal
            details_window.transient(self.frame)
            details_window.grab_set()
            self.frame.wait_window(details_window)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض تفاصيل السجل:\n{str(e)}")
            
    def update_summary(self, records):
        """Update the summary display with search results information"""
        # Clear existing widgets
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
            
        if not records:
            # Show message if no records found
            ttk.Label(
                self.summary_frame,
                text="لم يتم العثور على أي سجلات تطابق معايير البحث",
                wraplength=600,
                justify=tk.CENTER,
                padding=20
            ).pack(fill=tk.X)
            return
            
        # Create summary statistics
        summary_frame = ttk.Frame(self.summary_frame)
        summary_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Calculate totals
        total_records = len(records)
        total_salary = sum(record["prim_salary_c"] for record in records)
        total_entitlements = sum(record["tin"] for record in records)
        total_deductions = sum(record["tout"] for record in records)
        total_net = sum(record["alsafi"] for record in records)
        
        # Create two columns for statistics
        left_frame = ttk.Frame(summary_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        right_frame = ttk.Frame(summary_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        # Left column statistics
        ttk.Label(left_frame, text=f"عدد السجلات: {total_records}", font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=2)
        ttk.Label(left_frame, text=f"إجمالي الرواتب الاسمية: {total_salary:.2f}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        ttk.Label(left_frame, text=f"إجمالي الاستحقاقات: {total_entitlements:.2f}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        
        # Right column statistics
        ttk.Label(right_frame, text=f"متوسط الراتب الصافي: {(total_net/total_records if total_records else 0):.2f}", font=("Arial", 11, "bold")).pack(anchor=tk.W, pady=2)
        ttk.Label(right_frame, text=f"إجمالي الاستقطاعات: {total_deductions:.2f}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        ttk.Label(right_frame, text=f"إجمالي الصافي: {total_net:.2f}", font=("Arial", 11, "bold"), foreground="green").pack(anchor=tk.W, pady=2)
        
        # Add department statistics if multiple departments
        departments = {}
        for record in records:
            dept = record["department_name"]
            if dept not in departments:
                departments[dept] = {
                    "count": 0,
                    "total_salary": 0,
                    "total_net": 0
                }
                
            departments[dept]["count"] += 1
            departments[dept]["total_salary"] += record["prim_salary_c"]
            departments[dept]["total_net"] += record["alsafi"]
            
        if len(departments) > 1:
            # Create department statistics frame
            dept_frame = ttk.LabelFrame(self.summary_frame, text="إحصائيات حسب المجموعة")
            dept_frame.pack(fill=tk.X, padx=20, pady=10)
            
            # Create header
            header_frame = ttk.Frame(dept_frame)
            header_frame.pack(fill=tk.X, padx=5, pady=5)
            
            ttk.Label(header_frame, text="المجموعة", width=20, font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            ttk.Label(header_frame, text="عدد الموظفين", width=15, font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            ttk.Label(header_frame, text="إجمالي الرواتب", width=15, font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            ttk.Label(header_frame, text="متوسط الراتب", width=15, font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            
            # Add department rows
            for dept_name, stats in departments.items():
                row_frame = ttk.Frame(dept_frame)
                row_frame.pack(fill=tk.X, padx=5, pady=2)
                
                ttk.Label(row_frame, text=dept_name, width=20).pack(side=tk.LEFT, padx=5)
                ttk.Label(row_frame, text=str(stats["count"]), width=15).pack(side=tk.LEFT, padx=5)
                ttk.Label(row_frame, text=f"{stats['total_salary']:.2f}", width=15).pack(side=tk.LEFT, padx=5)
                avg_salary = stats["total_net"] / stats["count"] if stats["count"] else 0
                ttk.Label(row_frame, text=f"{avg_salary:.2f}", width=15).pack(side=tk.LEFT, padx=5)
        
    def refresh_data(self):
        """Refresh the displayed data"""
        self.load_departments()
        self.load_employees()
        
        # Clear results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
            
        # Clear chart
        for widget in self.chart_frame.winfo_children():
            widget.destroy()