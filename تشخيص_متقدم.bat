@echo off
echo ===== تشخيص متقدم لنظام إدارة رواتب الموظفين =====
echo.

REM التحقق من وجود Python
echo التحقق من وجود Python...
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [خطأ] Python غير موجود في النظام أو غير مضاف إلى متغير PATH
    echo يرجى تشغيل ملف تثبيت_Python.bat أولاً
) else (
    echo [نجاح] تم العثور على Python
    python --version
)

echo.
echo التحقق من المكتبات المطلوبة...

REM التحقق من وجود tkinter
python -c "import tkinter; print('tkinter version:', tkinter.TkVersion)" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [خطأ] tkinter غير موجود
    echo tkinter هو جزء من تثبيت Python القياسي. يرجى إعادة تثبيت Python مع تفعيل خيار "tcl/tk and IDLE"
) else (
    echo [نجاح] tkinter موجود
)

REM التحقق من وجود pandas
python -c "import pandas; print('pandas version:', pandas.__version__)" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [خطأ] pandas غير موجود
    echo يرجى تشغيل ملف install_dependencies.bat لتثبيت المكتبات المطلوبة
) else (
    echo [نجاح] pandas موجود
)

REM التحقق من وجود matplotlib
python -c "import matplotlib; print('matplotlib version:', matplotlib.__version__)" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [خطأ] matplotlib غير موجود
    echo يرجى تشغيل ملف install_dependencies.bat لتثبيت المكتبات المطلوبة
) else (
    echo [نجاح] matplotlib موجود
)

REM التحقق من وجود pywin32
python -c "import win32gui, win32con; print('pywin32 موجود')" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [خطأ] pywin32 غير موجود
    echo يرجى تشغيل ملف install_dependencies.bat لتثبيت المكتبات المطلوبة
) else (
    echo [نجاح] pywin32 موجود
)

echo.
echo التحقق من وجود الملفات الضرورية...
if exist app\main.py (
    echo [نجاح] تم العثور على ملف main.py
) else (
    echo [خطأ] ملف main.py غير موجود!
)

if exist app\ui\main_window.py (
    echo [نجاح] تم العثور على ملف main_window.py
) else (
    echo [خطأ] ملف main_window.py غير موجود!
)

if exist app\database\db_manager.py (
    echo [نجاح] تم العثور على ملف db_manager.py
) else (
    echo [خطأ] ملف db_manager.py غير موجود!
)

echo.
echo محاولة تشغيل البرنامج مع تعطيل إخفاء نافذة وحدة التحكم...
echo سيتم إنشاء ملف مؤقت لتشغيل البرنامج بدون إخفاء نافذة وحدة التحكم

REM إنشاء ملف Python مؤقت
echo import os > temp_run.py
echo import sys >> temp_run.py
echo sys.path.append(os.path.dirname(os.path.abspath(__file__))) >> temp_run.py
echo from app.main import Application >> temp_run.py
echo if __name__ == "__main__": >> temp_run.py
echo     try: >> temp_run.py
echo         app = Application() >> temp_run.py
echo         app.run() >> temp_run.py
echo     except Exception as e: >> temp_run.py
echo         print(f"Error: {e}") >> temp_run.py
echo         import traceback >> temp_run.py
echo         traceback.print_exc() >> temp_run.py
echo         input("Press Enter to exit...") >> temp_run.py

REM تشغيل الملف المؤقت
python temp_run.py

REM حذف الملف المؤقت
del temp_run.py

echo.
echo انتهى التشخيص المتقدم
pause