#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Employee Record Tab for Employee Salary Management System
Displays monthly records for employees
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class EmployeeRecordTab:
    """Tab for displaying employee monthly records"""
    
    def __init__(self, parent, db_manager):
        """Initialize the employee record tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Initialize variables
        self.current_employee_id = None
        self.current_year = None
        self.record_tree = None  # Initialize record_tree to None
        
        # Create form elements
        self.create_record_form()
        
    def round_number(self, value):
        """تقريب الأرقام العشرية وحذف النقاط العشرية
        إذا كان الرقم بعد النقطة من 5 إلى 9، يتم تقريبه إلى 1
        مثال: 540999.7777 تصبح 541000
        """
        try:
            # تحويل القيمة إلى رقم إذا لم تكن رقماً بالفعل
            value = float(value)
            
            # تقريب الرقم إلى أقرب عدد صحيح
            # إذا كان الجزء العشري >= 0.5، يتم تقريبه إلى الأعلى
            rounded_value = round(value)
            
            # تحويل الرقم إلى عدد صحيح (بدون نقطة عشرية)
            return int(rounded_value)
        except:
            # إذا لم يكن رقماً، إرجاع القيمة كما هي
            return value
        
    def create_record_form(self):
        """Create the employee record form"""
        # Main container with two frames
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left frame for selection and controls
        left_frame = ttk.Frame(main_frame, width=250)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        left_frame.pack_propagate(False)  # Prevent frame from shrinking
        
        # Right frame for record display
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create selection controls
        self.create_selection_controls(left_frame)
        
        # Create record display
        self.create_record_display(right_frame)
        
        # Create chart display
        self.create_chart_display(right_frame)
        
    def create_selection_controls(self, parent):
        """Create employee and year selection controls"""
        # Employee selection
        ttk.Label(parent, text="اختيار الموظف:").pack(anchor=tk.W, padx=5, pady=5)
        self.employee_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(parent, textvariable=self.employee_var, width=30)
        self.employee_combo.pack(fill=tk.X, padx=5, pady=2)
        self.employee_combo.bind("<<ComboboxSelected>>", self.on_employee_selected)
        
        # Department filter
        ttk.Label(parent, text="تصفية حسب المجموعة:").pack(anchor=tk.W, padx=5, pady=5)
        self.department_var = tk.StringVar()
        self.department_combo = ttk.Combobox(parent, textvariable=self.department_var, width=30)
        self.department_combo.pack(fill=tk.X, padx=5, pady=2)
        self.department_combo.bind("<<ComboboxSelected>>", self.on_department_selected)
        
        # Year selection
        ttk.Label(parent, text="السنة:").pack(anchor=tk.W, padx=5, pady=5)
        self.year_var = tk.StringVar()
        import datetime
        current_year = datetime.datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 6)]
        self.year_combo = ttk.Combobox(parent, textvariable=self.year_var, values=years, width=10)
        self.year_combo.pack(anchor=tk.W, padx=5, pady=2)
        self.year_var.set(str(current_year))
        self.year_combo.bind("<<ComboboxSelected>>", self.on_year_selected)
        
        # Refresh button
        ttk.Button(
            parent, 
            text="تحديث البيانات", 
            command=self.refresh_data
        ).pack(fill=tk.X, padx=5, pady=10)
        
        # Navigation buttons frame - completely redesigned with grid layout
        nav_frame = ttk.LabelFrame(parent, text="أزرار التنقل")
        nav_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create a grid for buttons - this ensures all buttons are visible
        buttons_grid = ttk.Frame(nav_frame)
        buttons_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Configure grid columns to have equal width
        buttons_grid.columnconfigure(0, weight=1)
        buttons_grid.columnconfigure(1, weight=1)
        buttons_grid.columnconfigure(2, weight=1)
        buttons_grid.columnconfigure(3, weight=1)
        
        # Create all four buttons with very small width - from right to left
        # الأول (First record) - rightmost button
        first_btn = ttk.Button(
            buttons_grid, 
            text="الأول", 
            command=self.go_to_first_record,
            width=6
        )
        first_btn.grid(row=0, column=3, padx=2, pady=5, sticky="ew")
        
        # التالي (Next)
        next_btn = ttk.Button(
            buttons_grid, 
            text="التالي", 
            command=self.go_to_next_record,
            width=6
        )
        next_btn.grid(row=0, column=2, padx=2, pady=5, sticky="ew")
        
        # السابق (Previous)
        prev_btn = ttk.Button(
            buttons_grid, 
            text="السابق", 
            command=self.go_to_prev_record,
            width=6
        )
        prev_btn.grid(row=0, column=1, padx=2, pady=5, sticky="ew")
        
        # الأخير (Last record) - leftmost button
        last_btn = ttk.Button(
            buttons_grid, 
            text="الأخير", 
            command=self.go_to_last_record,
            width=6
        )
        last_btn.grid(row=0, column=0, padx=2, pady=5, sticky="ew")
        
        # Print and export buttons frame
        action_frame = ttk.Frame(parent)
        action_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Print button (green)
        print_button = ttk.Button(
            action_frame, 
            text="طباعة واجهة موظف", 
            command=self.print_employee_record,
            style="Green.TButton"
        )
        print_button.pack(fill=tk.X, padx=2, pady=5)
        
        # Export button
        ttk.Button(
            action_frame,
            text="تصدير واجهة الموظف إلى Excel",
            command=self.export_to_excel
        ).pack(fill=tk.X, padx=2, pady=5)

        # Return to main button
        ttk.Button(
            action_frame,
            text="العودة للواجهة الرئيسية",
            command=self.return_to_main,
            style="Blue.TButton"
        ).pack(fill=tk.X, padx=2, pady=5)
        
        # Configure green button style
        style = ttk.Style()
        style.configure("Green.TButton", background="green", foreground="white")
        
        # Load departments and employees
        self.load_departments()
        self.load_employees()

    def return_to_main(self):
        """Return to main interface by switching to the first tab"""
        # Get the parent notebook and switch to the first tab
        parent_notebook = self.frame.master
        if hasattr(parent_notebook, 'select'):
            parent_notebook.select(0)  # Select the first tab
        
    def create_record_display(self, parent):
        """Create the record display area"""
        # Create a frame for the treeview
        tree_frame = ttk.LabelFrame(parent, text="سجل الموظف الشهري")
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create scrollbars
        vsb = ttk.Scrollbar(tree_frame, orient="vertical")
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal")
        
        # Create treeview
        self.record_tree = ttk.Treeview(
            tree_frame,
            yscrollcommand=vsb.set,
            xscrollcommand=hsb.set
        )
        
        # Configure scrollbars
        vsb.config(command=self.record_tree.yview)
        hsb.config(command=self.record_tree.xview)
        
        # Place scrollbars (RTL layout)
        vsb.pack(side=tk.LEFT, fill=tk.Y)  # Changed from RIGHT to LEFT for RTL
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Configure treeview columns (RTL order)
        self.record_tree["columns"] = (
            "note", "alsafi",
            "separator2",
            "total_deductions", "eijar", "rasheed", "raseem", "tanfeeth", "eskan", "eikari", "reaya", "dariba", "tokifat",
            "separator1",
            "total_entitlements", "nakil", "incom_gc", "arzaak", "denger", "mansib", "athfal", "zawjiya", "prim_salary",
            "month"
        )
        
        self.record_tree.column("#0", width=0, stretch=tk.NO)
        
        # Month (now on the right side)
        self.record_tree.column("month", width=100, anchor=tk.E)  # Changed to right-aligned
        
        # Entitlements (RTL order)
        self.record_tree.column("prim_salary", width=80, anchor=tk.W)  # Changed to left-aligned in RTL
        self.record_tree.column("zawjiya", width=80, anchor=tk.W)
        self.record_tree.column("athfal", width=80, anchor=tk.W)
        self.record_tree.column("mansib", width=80, anchor=tk.W)
        self.record_tree.column("denger", width=80, anchor=tk.W)
        self.record_tree.column("arzaak", width=80, anchor=tk.W)
        self.record_tree.column("incom_gc", width=80, anchor=tk.W)
        self.record_tree.column("nakil", width=80, anchor=tk.W)
        self.record_tree.column("total_entitlements", width=100, anchor=tk.W)
        
        # Separator
        self.record_tree.column("separator1", width=20, stretch=tk.NO)
        
        # Deductions (RTL order)
        self.record_tree.column("tokifat", width=80, anchor=tk.W)
        self.record_tree.column("dariba", width=80, anchor=tk.W)
        self.record_tree.column("reaya", width=80, anchor=tk.W)
        self.record_tree.column("eikari", width=80, anchor=tk.W)
        self.record_tree.column("eskan", width=80, anchor=tk.W)
        self.record_tree.column("tanfeeth", width=80, anchor=tk.W)
        self.record_tree.column("raseem", width=80, anchor=tk.W)
        self.record_tree.column("rasheed", width=80, anchor=tk.W)
        self.record_tree.column("eijar", width=80, anchor=tk.W)
        self.record_tree.column("total_deductions", width=100, anchor=tk.W)
        
        # Separator
        self.record_tree.column("separator2", width=20, stretch=tk.NO)
        
        # Net salary and notes (now on the left side)
        self.record_tree.column("alsafi", width=100, anchor=tk.W)
        self.record_tree.column("note", width=300, anchor=tk.E)  # Increased width for notes and right-aligned in RTL
        
        # Configure headings (RTL order)
        # Notes and Net salary (now on the left side)
        self.record_tree.heading("note", text="الملاحظات")
        self.record_tree.heading("alsafi", text="الصافي")
        
        # Separator
        self.record_tree.heading("separator2", text="")
        
        # Deductions (RTL order)
        self.record_tree.heading("total_deductions", text="م. الاستقطاعات")
        self.record_tree.heading("eijar", text="الإيجار")
        self.record_tree.heading("rasheed", text="الرشيد")
        self.record_tree.heading("raseem", text="الطابع")
        self.record_tree.heading("tanfeeth", text="التنفيذ")
        self.record_tree.heading("eskan", text="الإسكان")
        self.record_tree.heading("eikari", text="العقار")
        self.record_tree.heading("reaya", text="الرعاية")
        self.record_tree.heading("dariba", text="الضريبة")
        self.record_tree.heading("tokifat", text="التقاعدية")
        
        # Separator
        self.record_tree.heading("separator1", text="")
        
        # Entitlements (RTL order)
        self.record_tree.heading("total_entitlements", text="م. الاستحقاقات")
        self.record_tree.heading("nakil", text="م. النقل")
        self.record_tree.heading("incom_gc", text="م. الجامعية")
        self.record_tree.heading("arzaak", text="م. المهنية")
        self.record_tree.heading("denger", text="م. الخطورة")
        self.record_tree.heading("mansib", text="المنصب")
        self.record_tree.heading("athfal", text="م. الأطفال")
        self.record_tree.heading("zawjiya", text="م. الزوجية")
        self.record_tree.heading("prim_salary", text="الراتب الاسمي")
        
        # Month (now on the right side)
        self.record_tree.heading("month", text="الشهر")
        
        # Apply tag for separators
        self.record_tree.tag_configure("separator", background="gray")
        
        # Apply tag for net salary
        self.record_tree.tag_configure("net", background="#e6ffe6")
        
        # Apply tag for total entitlements
        self.record_tree.tag_configure("entitlements", background="#E6FFE6")  # Light green
        
        # Apply tag for total deductions
        self.record_tree.tag_configure("deductions", background="#FFE6CC")  # Light orange
        
        # Pack treeview
        self.record_tree.pack(fill=tk.BOTH, expand=True)
        
        # Pre-populate with empty rows for all months
        self.populate_empty_months()
        
    def populate_empty_months(self):
        """Pre-populate the treeview with empty rows for all months"""
        # Check if record_tree exists
        if not self.record_tree:
            return
            
        # Arabic month names
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        
        # Create empty values for all columns
        empty_values = ["0"] * (len(self.record_tree["columns"]) - 3)  # Exclude month, separator columns, and note
        
        # Add empty rows for each month
        for i, month_name in enumerate(months):
            # Create values list for RTL order
            # Format: [note, alsafi, separator2, deductions..., separator1, entitlements..., month]
            values = [""] + ["0"] * 21  # Empty note and zeros for all numeric fields
            
            # Insert separator values at the correct positions
            values.insert(2, "|")  # After alsafi
            values.insert(13, "|")  # After deductions
            
            # Add month name at the end (rightmost column in RTL)
            values.append(month_name)
            
            # Add the row to the treeview
            self.record_tree.insert("", tk.END, values=values, tags=("empty",), iid=f"month_{i+1}")
            
    def create_chart_display(self, parent):
        """Create the chart display area"""
        # Create a frame for additional information instead of chart
        self.info_frame = ttk.LabelFrame(parent, text="معلومات إضافية")
        self.info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Add some helpful text
        info_label = ttk.Label(
            self.info_frame, 
            text="يمكنك استيراد بيانات الموظف من تبويب 'جلب البيانات من ملف Excel' أو إدخالها يدوياً من تبويب 'إدخال بيانات الموظف'.",
            wraplength=600,
            justify=tk.CENTER,
            padding=20
        )
        info_label.pack(fill=tk.X)
        
    def load_departments(self):
        """Load departments into the combobox"""
        departments = self.db_manager.get_departments()
        department_names = ["اختر القسم..."] + [dept["name"] for dept in departments]
        self.department_combo["values"] = department_names
        self.department_var.set("اختر القسم...")
        
    def load_employees(self, department_id=None):
        """Load employees into the combobox - only those with salary records for the selected year"""
        # Get all employees from the department
        all_employees = self.db_manager.get_employees(department_id)

        # Get the selected year
        try:
            selected_year = int(self.year_var.get())
        except:
            selected_year = 2024  # Default year if none selected

        # Filter employees who have salary records for the selected year
        employees_with_records = []
        for emp in all_employees:
            # Check if this employee has any salary records for the selected year
            records = self.db_manager.get_employee_salary_records(emp["id"], selected_year)
            if records:  # Only include employees who have records
                employees_with_records.append(emp)

        # Create display names and data dictionary
        employee_names = [f"{emp['name']} ({emp['department_name']})" for emp in employees_with_records]
        self.employee_data = {f"{emp['name']} ({emp['department_name']})": emp for emp in employees_with_records}
        self.employee_combo["values"] = employee_names

        if employee_names:
            self.employee_var.set(employee_names[0])
            self.on_employee_selected(None)
        else:
            # Clear selection if no employees with records found
            self.employee_var.set("")
            self.current_employee_id = None
            # Clear the record tree
            if self.record_tree:
                for item in self.record_tree.get_children():
                    self.record_tree.delete(item)
            # Show message in info area
            if hasattr(self, 'info_frame'):
                for widget in self.info_frame.winfo_children():
                    widget.destroy()
                info_label = ttk.Label(
                    self.info_frame,
                    text=f"لا توجد سجلات راتب للموظفين في هذه المجموعة للسنة {selected_year}.",
                    wraplength=600,
                    justify=tk.CENTER,
                    padding=20,
                    font=("Arial", 12)
                )
                info_label.pack(fill=tk.X)
            
    def on_department_selected(self, event):
        """Handle department selection"""
        selected = self.department_var.get()

        if selected == "اختر القسم..." or not selected:
            # لا تحمل أي موظفين عند اختيار "اختر القسم..."
            # اطلب من المستخدم اختيار قسم محدد
            self.employee_combo["values"] = []
            self.employee_var.set("")
            self.current_employee_id = None
            # مسح الجدول
            if self.record_tree:
                for item in self.record_tree.get_children():
                    self.record_tree.delete(item)
            # إظهار رسالة في منطقة المعلومات
            if hasattr(self, 'info_frame'):
                for widget in self.info_frame.winfo_children():
                    widget.destroy()
                info_label = ttk.Label(
                    self.info_frame,
                    text="يرجى اختيار قسم محدد من القائمة المنسدلة لعرض الموظفين المرتبطين به.",
                    wraplength=600,
                    justify=tk.CENTER,
                    padding=20,
                    font=("Arial", 12)
                )
                info_label.pack(fill=tk.X)
        else:
            # Get department ID
            departments = self.db_manager.get_departments()
            department_id = None
            for dept in departments:
                if dept["name"] == selected:
                    department_id = dept["id"]
                    break

            if department_id:
                # Load employees for this department
                self.load_employees(department_id)
                
    def on_employee_selected(self, event):
        """Handle employee selection"""
        selected = self.employee_var.get()
        if selected in self.employee_data:
            employee = self.employee_data[selected]
            self.current_employee_id = employee["id"]
            # Only load records if record_tree exists
            if self.record_tree:
                self.load_employee_records()
            
    def on_year_selected(self, event):
        """Handle year selection"""
        self.current_year = int(self.year_var.get())

        # Reload employees for the new year (only those with records)
        current_department = self.department_var.get()
        if current_department and current_department != "اختر القسم...":
            # Get department ID
            departments = self.db_manager.get_departments()
            department_id = None
            for dept in departments:
                if dept["name"] == current_department:
                    department_id = dept["id"]
                    break

            if department_id:
                # Reload employees for this department and year
                self.load_employees(department_id)

        # Only load records if record_tree exists
        if self.record_tree:
            self.load_employee_records()
        
    def load_employee_records(self):
        """Load records for the selected employee and year"""
        if not self.current_employee_id:
            return
            
        # Check if record_tree exists
        if not self.record_tree:
            return
            
        try:
            year = int(self.year_var.get())
            self.current_year = year
            
            # Get records for this employee and year
            records = self.db_manager.get_employee_salary_records(self.current_employee_id, year)
            
            # Clear existing items in treeview
            for item in self.record_tree.get_children():
                self.record_tree.delete(item)
                
            # Pre-populate with empty rows for all months
            self.populate_empty_months()
            
            # Arabic month names
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            
            # Add records to treeview
            for record in records:
                month_idx = record["month"] - 1
                month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
                
                # Delete the empty row for this month
                self.record_tree.delete(f"month_{record['month']}")
                
                # Values in RTL order with rounded numbers
                values = (
                    record["note"],
                    self.round_number(record["alsafi"]),
                    "|",  # Separator
                    self.round_number(record["tout"]),  # Total deductions
                    self.round_number(record["eijar"]),
                    self.round_number(record["rasheed"]),
                    self.round_number(record["raseem"]),
                    self.round_number(record["tanfeeth"]),
                    self.round_number(record["eskan"]),
                    self.round_number(record["eikari"]),
                    self.round_number(record["reaya"]),
                    self.round_number(record["dariba"]),
                    self.round_number(record["tokifat"]),
                    "|",  # Separator
                    self.round_number(record["tin"]),  # Total entitlements
                    self.round_number(record["nakil"]),
                    self.round_number(record["incom_gc"]),
                    self.round_number(record["arzaak"]),
                    self.round_number(record["denger"]),
                    self.round_number(record["mansib_c"]),
                    self.round_number(record["athfal"]),
                    self.round_number(record["zawjiya"]),
                    self.round_number(record["prim_salary_c"]),
                    month_name
                )
                
                # Insert with the month number as the ID for easy reference
                item_id = self.record_tree.insert("", month_idx, iid=f"month_{record['month']}", values=values)
                
                # Apply tags
                self.record_tree.item(item_id, tags=("record",))
                
            # Update the info frame with employee summary
            self.update_info_frame(records)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل سجلات الموظف:\n{str(e)}")
            
    def update_info_frame(self, records):
        """Update the info frame with employee summary"""
        # Clear existing widgets
        for widget in self.info_frame.winfo_children():
            widget.destroy()
            
        if not records:
            # Show default message if no records
            info_label = ttk.Label(
                self.info_frame, 
                text="لا توجد سجلات للموظف في السنة المحددة. يمكنك استيراد البيانات من تبويب 'جلب البيانات من ملف Excel'.",
                wraplength=600,
                justify=tk.CENTER,
                padding=20
            )
            info_label.pack(fill=tk.X)
            return
            
        # Get employee info
        employee = None
        for emp in self.employee_data.values():
            if emp["id"] == self.current_employee_id:
                employee = emp
                break
                
        if not employee:
            return
            
        # Create summary frame
        summary_frame = ttk.Frame(self.info_frame)
        summary_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Employee info
        info_text = f"الموظف: {employee['name']} - {employee['job_title']} - {employee['department_name']}"
        ttk.Label(summary_frame, text=info_text, font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=5)
        
        # Calculate yearly totals
        total_salary = sum(record["prim_salary_c"] for record in records)
        total_entitlements = sum(record["tin"] for record in records)
        total_deductions = sum(record["tout"] for record in records)
        total_net = sum(record["alsafi"] for record in records)
        
        # Create totals frame with two columns
        totals_frame = ttk.Frame(self.info_frame)
        totals_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Left column
        left_frame = ttk.Frame(totals_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(left_frame, text=f"إجمالي الراتب الاسمي للسنة: {self.round_number(total_salary)}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        ttk.Label(left_frame, text=f"إجمالي الاستحقاقات للسنة: {self.round_number(total_entitlements)}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        
        # Right column
        right_frame = ttk.Frame(totals_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        ttk.Label(right_frame, text=f"إجمالي الاستقطاعات للسنة: {self.round_number(total_deductions)}", font=("Arial", 11)).pack(anchor=tk.W, pady=2)
        ttk.Label(right_frame, text=f"إجمالي الصافي للسنة: {self.round_number(total_net)}", font=("Arial", 11, "bold"), foreground="green").pack(anchor=tk.W, pady=2)
        
        # Add monthly average
        avg_frame = ttk.Frame(self.info_frame)
        avg_frame.pack(fill=tk.X, padx=20, pady=10)
        
        num_months = len(records)
        if num_months > 0:
            avg_net = total_net / num_months
            ttk.Label(avg_frame, text=f"متوسط الراتب الشهري: {self.round_number(avg_net)}", font=("Arial", 12, "bold")).pack(anchor=tk.CENTER, pady=5)
            
    # Removed the create_salary_chart method as requested
            
    def refresh_data(self):
        """Refresh the displayed data"""
        # احفظ الموظف المحدد حالياً
        current_employee_selection = self.employee_var.get() if hasattr(self, 'employee_var') else None
        current_department_selection = self.department_var.get() if hasattr(self, 'department_var') else None

        # تحديث قوائم الأقسام والموظفين
        self.load_departments()

        # استعادة اختيار القسم إذا كان موجوداً وليس "اختر القسم..."
        if (current_department_selection and
            current_department_selection != "اختر القسم..." and
            current_department_selection in self.department_combo['values']):
            self.department_var.set(current_department_selection)
            self.on_department_selected(None)
        else:
            # إذا لم يكن هناك قسم محدد، لا تحمل أي موظفين
            # اتركها فارغة حتى يختار المستخدم قسماً
            self.employee_combo["values"] = []
            self.employee_var.set("")
            self.current_employee_id = None
            # مسح الجدول
            if self.record_tree:
                for item in self.record_tree.get_children():
                    self.record_tree.delete(item)
            # إظهار رسالة في منطقة المعلومات
            if hasattr(self, 'info_frame'):
                for widget in self.info_frame.winfo_children():
                    widget.destroy()
                info_label = ttk.Label(
                    self.info_frame,
                    text="يرجى اختيار قسم محدد من القائمة المنسدلة لعرض الموظفين المرتبطين به.",
                    wraplength=600,
                    justify=tk.CENTER,
                    padding=20,
                    font=("Arial", 12)
                )
                info_label.pack(fill=tk.X)
            return

        # استعادة اختيار الموظف إذا كان موجوداً
        if current_employee_selection and current_employee_selection in self.employee_combo['values']:
            self.employee_var.set(current_employee_selection)
            self.on_employee_selected(None)

        # تحديث سجلات الموظف المحدد فقط
        if self.record_tree and self.current_employee_id:
            self.load_employee_records()
            
    def go_to_first_record(self):
        """Go to the first employee record"""
        if not self.employee_combo['values']:
            messagebox.showinfo("معلومات", "لا توجد سجلات للموظفين")
            return

        # التحقق من وجود تصفية حسب المجموعة
        current_department = self.department_var.get()
        if current_department == "اختر القسم...":
            messagebox.showinfo("معلومات", "يرجى اختيار قسم محدد أولاً")
            return

        self.employee_combo.current(0)
        self.on_employee_selected(None)
        
    def go_to_next_record(self):
        """Go to the next employee record"""
        if not self.employee_combo['values']:
            return

        # التحقق من وجود تصفية حسب المجموعة
        current_department = self.department_var.get()
        if current_department == "اختر القسم...":
            messagebox.showinfo("معلومات", "يرجى اختيار قسم محدد أولاً")
            return

        current_index = self.employee_combo.current()
        if current_index < len(self.employee_combo['values']) - 1:
            self.employee_combo.current(current_index + 1)
            self.on_employee_selected(None)
        else:
            messagebox.showinfo("معلومات", "أنت بالفعل في آخر سجل للمجموعة المحددة")
            
    def go_to_prev_record(self):
        """Go to the previous employee record"""
        if not self.employee_combo['values']:
            return

        # التحقق من وجود تصفية حسب المجموعة
        current_department = self.department_var.get()
        if current_department == "اختر القسم...":
            messagebox.showinfo("معلومات", "يرجى اختيار قسم محدد أولاً")
            return

        current_index = self.employee_combo.current()
        if current_index > 0:
            self.employee_combo.current(current_index - 1)
            self.on_employee_selected(None)
        else:
            messagebox.showinfo("معلومات", "أنت بالفعل في أول سجل للمجموعة المحددة")
            
    def go_to_last_record(self):
        """Go to the last employee record"""
        if not self.employee_combo['values']:
            messagebox.showinfo("معلومات", "لا توجد سجلات للموظفين")
            return

        # التحقق من وجود تصفية حسب المجموعة
        current_department = self.department_var.get()
        if current_department == "اختر القسم...":
            messagebox.showinfo("معلومات", "يرجى اختيار قسم محدد أولاً")
            return

        self.employee_combo.current(len(self.employee_combo['values']) - 1)
        self.on_employee_selected(None)
        
    def print_employee_record(self):
        """Print the current employee record"""
        if not self.current_employee_id:
            messagebox.showinfo("معلومات", "يرجى اختيار موظف أولاً")
            return
            
        try:
            # Get employee info
            employee = None
            for emp in self.employee_data.values():
                if emp["id"] == self.current_employee_id:
                    employee = emp
                    break
                    
            if not employee:
                messagebox.showinfo("معلومات", "لم يتم العثور على بيانات الموظف")
                return
                
            # Create a temporary file for printing
            import tempfile
            import os
            import webbrowser
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html')
            temp_file.close()
            
            # Generate HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>سجل الرواتب محاسبة / ٨</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 10px; direction: rtl; }}
                    h1 {{ text-align: center; color: #333; margin-bottom: 20px; }}
                    .employee-info {{ background-color: #f5f5f5; padding: 10px; margin-bottom: 10px; border-radius: 5px; }}

                    /* جدول الأولاد */
                    .children-table {{
                        width: 60%;
                        margin: 20px auto;
                        border-collapse: collapse;
                        font-size: 12px;
                        border: 2px solid #333;
                    }}
                    .children-table th {{
                        border: 1px solid #333;
                        padding: 8px;
                        text-align: center;
                        background-color: #f0f0f0;
                        font-weight: bold;
                    }}
                    .children-table td {{
                        border: 1px solid #333;
                        padding: 8px;
                        text-align: center;
                        height: 25px;
                    }}
                    .children-table .name-column {{
                        width: 40%;
                        font-weight: bold;
                    }}
                    .children-table .serial-column {{ width: 15%; }}
                    .children-table .date-column {{ width: 25%; }}
                    .children-table .profession-column {{ width: 20%; }}

                    /* جدول الرواتب */
                    .salary-table {{ width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 11px; }}
                    .salary-table th, .salary-table td {{ border: 1px solid #ddd; padding: 4px; text-align: right; }}
                    .salary-table th {{ background-color: #f2f2f2; }}
                    .salary-table tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    .total {{ font-weight: bold; background-color: #e6ffe6; }}
                    .footer {{ margin-top: 20px; text-align: center; font-size: 10px; color: #777; }}
                    .note {{ width: 120px; }}

                    @media print {{
                        body {{ margin: 0.5cm; }}
                        button {{ display: none; }}
                        @page {{ size: landscape; margin: 0.5cm; }}
                        .salary-table {{ transform: scale(0.95); transform-origin: top right; }}
                        .salary-table th, .salary-table td {{ font-size: 9px !important; padding: 2px !important; }}
                        .children-table {{ font-size: 10px; }}
                        .children-table th, .children-table td {{ padding: 5px; }}
                    }}
                </style>
            </head>
            <body>
                <h1>سجل الرواتب محاسبة / ٨</h1>

                <!-- جدول الأولاد -->
                <table class="children-table">
                    <tr>
                        <th class="serial-column">ت</th>
                        <th class="name-column">اسم الولد</th>
                        <th class="date-column">تاريخ الولادة</th>
                        <th class="profession-column">المهنة أو المرحلة الدراسية</th>
                    </tr>
                    <tr>
                        <td>1</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td class="name-column"></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>

                <div class="employee-info">
                    <h2>{employee['name']}</h2>
                    <p>العنوان الوظيفي: {employee['job_title']}</p>
                    <p>المجموعة: {employee['department_name']}</p>
                    <p>السنة: {self.year_var.get()}</p>
                </div>
                
                <table class="salary-table">
                    <tr>
                        <th>الشهر</th>
                        <th>الراتب الاسمي</th>
                        <th>م.الزوجية</th>
                        <th>م.الأولاد</th>
                        <th>م.المنصب</th>
                        <th>م.الخطورة</th>
                        <th>م.المهنية</th>
                        <th>م.الجامعية</th>
                        <th>م.النقل</th>
                        <th>م.الاستحقاق</th>
                        <th>ت.التقاعدية</th>
                        <th>الضريبة</th>
                        <th>الرعاية</th>
                        <th>العقار</th>
                        <th>الإسكان</th>
                        <th>التنفيذ</th>
                        <th>رسم طابع</th>
                        <th>الرشيد</th>
                        <th>الإيجار</th>
                        <th>م.الاستقطاعات</th>
                        <th>الصافي</th>
                        <th class="note">الملاحظات</th>
                    </tr>
            """
            
            # Get records
            records = self.db_manager.get_employee_salary_records(self.current_employee_id, int(self.year_var.get()))
            
            # Arabic month names
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            
            # Sort records by month number
            records = sorted(records, key=lambda x: x["month"])
            
            # Add records to HTML
            total_salary = 0
            total_zawjiya = 0
            total_athfal = 0
            total_mansib = 0
            total_denger = 0
            total_arzaak = 0
            total_incom_gc = 0
            total_nakil = 0
            total_entitlements = 0
            total_tokifat = 0
            total_dariba = 0
            total_reaya = 0
            total_eikari = 0
            total_eskan = 0
            total_tanfeeth = 0
            total_raseem = 0
            total_rasheed = 0
            total_eijar = 0
            total_deductions = 0
            total_net = 0
            
            for record in records:
                month_idx = record["month"] - 1
                month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
                
                # استخراج جميع البيانات من السجل
                prim_salary = self.round_number(record["prim_salary_c"])
                zawjiya = self.round_number(record["zawjiya"])
                athfal = self.round_number(record["athfal"])
                mansib = self.round_number(record["mansib_c"])
                denger = self.round_number(record["denger"])
                arzaak = self.round_number(record["arzaak"])
                incom_gc = self.round_number(record["incom_gc"])
                nakil = self.round_number(record["nakil"])
                entitlements = self.round_number(record["tin"])
                tokifat = self.round_number(record["tokifat"])
                dariba = self.round_number(record["dariba"])
                reaya = self.round_number(record["reaya"])
                eikari = self.round_number(record["eikari"])
                eskan = self.round_number(record["eskan"])
                tanfeeth = self.round_number(record["tanfeeth"])
                raseem = self.round_number(record["raseem"])
                rasheed = self.round_number(record["rasheed"])
                eijar = self.round_number(record["eijar"])
                deductions = self.round_number(record["tout"])
                net = self.round_number(record["alsafi"])
                note = record["note"] or ""
                
                # تجميع الإجماليات
                total_salary += prim_salary
                total_zawjiya += zawjiya
                total_athfal += athfal
                total_mansib += mansib
                total_denger += denger
                total_arzaak += arzaak
                total_incom_gc += incom_gc
                total_nakil += nakil
                total_entitlements += entitlements
                total_tokifat += tokifat
                total_dariba += dariba
                total_reaya += reaya
                total_eikari += eikari
                total_eskan += eskan
                total_tanfeeth += tanfeeth
                total_raseem += raseem
                total_rasheed += rasheed
                total_eijar += eijar
                total_deductions += deductions
                total_net += net
                
                html_content += f"""
                <tr>
                    <td>{month_name}</td>
                    <td>{prim_salary}</td>
                    <td>{zawjiya}</td>
                    <td>{athfal}</td>
                    <td>{mansib}</td>
                    <td>{denger}</td>
                    <td>{arzaak}</td>
                    <td>{incom_gc}</td>
                    <td>{nakil}</td>
                    <td>{entitlements}</td>
                    <td>{tokifat}</td>
                    <td>{dariba}</td>
                    <td>{reaya}</td>
                    <td>{eikari}</td>
                    <td>{eskan}</td>
                    <td>{tanfeeth}</td>
                    <td>{raseem}</td>
                    <td>{rasheed}</td>
                    <td>{eijar}</td>
                    <td>{deductions}</td>
                    <td>{net}</td>
                    <td class="note">{note}</td>
                </tr>
                """
            
            # Close the table without adding totals row
            html_content += """
            </table>
            
            <script>
                window.onload = function() {{
                    // إزالة رأس وتذييل الطباعة وتحسين إعدادات الطباعة
                    var style = document.createElement('style');
                    style.innerHTML = `
                        @page {{
                            margin: 0.3cm;
                            size: landscape;
                        }}
                        @media print {{
                            body {{ margin: 0.3cm; }}
                            .salary-table {{ transform: scale(0.95); transform-origin: top right; }}
                            .salary-table th, .salary-table td {{ font-size: 9px !important; padding: 2px !important; }}
                            .children-table {{ font-size: 10px; }}
                            .children-table th, .children-table td {{ padding: 5px; }}
                        }}
                    `;
                    document.head.appendChild(style);

                    // تعديل حجم جدول الرواتب ليناسب الصفحة
                    var salaryTable = document.querySelector('.salary-table');
                    if (salaryTable) {{
                        salaryTable.style.maxWidth = '100%';
                    }}

                    // طباعة مباشرة بدون معاينة بعد تأخير قصير للسماح بتحميل الصفحة بالكامل
                    setTimeout(function() {{
                        window.print();
                    }}, 800);
                }};
            </script>
            </body>
            </html>
            """
            
            # Write HTML to file
            with open(temp_file.name, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # Open in browser for printing
            webbrowser.open('file://' + temp_file.name)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
            
    def export_to_excel(self):
        """Export the current employee record to Excel"""
        if not self.current_employee_id:
            messagebox.showinfo("معلومات", "يرجى اختيار موظف أولاً")
            return
            
        try:
            # Check if required libraries are installed
            try:
                import pandas as pd
                from openpyxl import Workbook
                from openpyxl.styles import Font, Alignment, PatternFill
                from openpyxl.utils import get_column_letter
            except ImportError:
                messagebox.showerror(
                    "خطأ", 
                    "المكتبات المطلوبة غير مثبتة. يرجى تثبيت المكتبات التالية:\n"
                    "pip install pandas openpyxl"
                )
                return
                
            # Get employee info
            employee = None
            for emp in self.employee_data.values():
                if emp["id"] == self.current_employee_id:
                    employee = emp
                    break
                    
            if not employee:
                messagebox.showinfo("معلومات", "لم يتم العثور على بيانات الموظف")
                return
                
            # Get save file location
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="حفظ ملف Excel"
            )
            
            if not file_path:
                return  # User cancelled
                
            # Get records
            records = self.db_manager.get_employee_salary_records(self.current_employee_id, int(self.year_var.get()))
            
            # Arabic month names
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            
            # Prepare data for Excel
            data = []
            for record in records:
                month_idx = record["month"] - 1
                month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
                
                data.append({
                    "الشهر": month_name,
                    "الراتب الاسمي": self.round_number(record["prim_salary_c"]),
                    "مخصص الزوجية": self.round_number(record["zawjiya"]),
                    "مخصص الأطفال": self.round_number(record["athfal"]),
                    "مخصص المنصب": self.round_number(record["mansib_c"]),
                    "مخصص الخطورة": self.round_number(record["denger"]),
                    "مخصص المهنية": self.round_number(record["arzaak"]),
                    "مخصص الجامعية": self.round_number(record["incom_gc"]),
                    "مخصص النقل": self.round_number(record["nakil"]),
                    "مجموع الاستحقاقات": self.round_number(record["tin"]),
                    "التقاعدية": self.round_number(record["tokifat"]),
                    "الضريبة": self.round_number(record["dariba"]),
                    "الرعاية": self.round_number(record["reaya"]),
                    "العقار": self.round_number(record["eikari"]),
                    "الإسكان": self.round_number(record["eskan"]),
                    "التنفيذ": self.round_number(record["tanfeeth"]),
                    "الطابع": self.round_number(record["raseem"]),
                    "الرشيد": self.round_number(record["rasheed"]),
                    "الإيجار": self.round_number(record["eijar"]),
                    "مجموع الاستقطاعات": self.round_number(record["tout"]),
                    "الصافي": self.round_number(record["alsafi"]),
                    "الملاحظات": record["note"] or ""
                })
            
            # Create DataFrame
            df = pd.DataFrame(data)
            
            # Create Excel file
            wb = Workbook()
            ws = wb.active
            ws.title = "سجل الموظف الشهري"
            
            # Add title
            ws.merge_cells('A1:V1')
            cell = ws['A1']
            cell.value = "سجل الموظف الشهري"
            cell.font = Font(size=16, bold=True)
            cell.alignment = Alignment(horizontal='center')
            
            # Add employee info
            ws.merge_cells('A2:V2')
            cell = ws['A2']
            cell.value = f"الموظف: {employee['name']} - {employee['job_title']} - {employee['department_name']} - السنة: {self.year_var.get()}"
            cell.font = Font(size=12, bold=True)
            cell.alignment = Alignment(horizontal='center')
            
            # Add headers
            headers = list(df.columns)
            for col_num, header in enumerate(headers, 1):
                col_letter = get_column_letter(col_num)
                cell = ws[f"{col_letter}4"]
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center', wrap_text=True)
                cell.fill = PatternFill(start_color="D9D9D9", end_color="D9D9D9", fill_type="solid")
                ws.column_dimensions[col_letter].width = 15
            
            # Add data
            for row_num, row_data in enumerate(df.values, 5):
                for col_num, value in enumerate(row_data, 1):
                    col_letter = get_column_letter(col_num)
                    cell = ws[f"{col_letter}{row_num}"]
                    cell.value = value
                    cell.alignment = Alignment(horizontal='center')
            
            # Add totals
            total_row = len(df) + 5
            ws[f"A{total_row}"] = "المجموع"
            ws[f"A{total_row}"].font = Font(bold=True)
            
            # Calculate totals for numeric columns
            for col_num, header in enumerate(headers, 1):
                if header not in ["الشهر", "الملاحظات"]:
                    col_letter = get_column_letter(col_num)
                    cell = ws[f"{col_letter}{total_row}"]
                    cell.value = f"=SUM({col_letter}5:{col_letter}{total_row-1})"
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")
            
            # Save the workbook
            wb.save(file_path)
            
            messagebox.showinfo("تصدير", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")