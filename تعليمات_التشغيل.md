# تعليمات تشغيل نظام إدارة رواتب الموظفين

## ملفات التشغيل المتاحة

### 1. تشغيل_محسن.bat (الموصى به)
- **الوصف**: الملف الأفضل لتشغيل البرنامج
- **المميزات**: 
  - يتحقق من وجود البيئة الافتراضية تلقائياً
  - يشغل النسخة الكاملة إذا كانت المكتبات متوفرة
  - يتراجع إلى النسخة البسيطة في حالة وجود مشاكل
- **الاستخدام**: انقر نقرة مزدوجة على الملف

### 2. تشغيل_آمن.bat
- **الوصف**: يشغل النسخة الكاملة من البرنامج
- **المتطلبات**: يحتاج إلى تثبيت جميع المكتبات المطلوبة
- **الاستخدام**: للمستخدمين المتقدمين

### 3. تشغيل_بسيط.bat
- **الوصف**: يشغل نسخة مبسطة من البرنامج
- **المميزات**: لا يحتاج إلى مكتبات إضافية
- **القيود**: وظائف محدودة (بدون رسوم بيانية)

### 4. تثبيت_المكتبات.bat
- **الوصف**: يثبت المكتبات المطلوبة للبرنامج
- **الاستخدام**: شغل هذا الملف أولاً إذا كانت هناك رسائل خطأ

## حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError: No module named 'matplotlib'"
**الحل**: 
1. شغل ملف `تثبيت_المكتبات.bat`
2. أو استخدم `تشغيل_بسيط.bat` للنسخة البسيطة

### مشكلة: "Python غير موجود"
**الحل**:
1. شغل ملف `تثبيت_Python.bat`
2. أو حمل Python من الموقع الرسمي: https://www.python.org/downloads/

### مشكلة: النافذة تختفي بسرعة
**الحل**:
- استخدم `تشغيل_محسن.bat` الذي يتعامل مع هذه المشكلة تلقائياً

## ترتيب التشغيل الموصى به

1. **للمرة الأولى**: شغل `تثبيت_المكتبات.bat`
2. **للاستخدام العادي**: شغل `تشغيل_محسن.bat`
3. **في حالة المشاكل**: شغل `تشغيل_بسيط.bat`

## معلومات إضافية

- البرنامج يستخدم بيئة افتراضية (Virtual Environment) لإدارة المكتبات
- جميع المكتبات المطلوبة مثبتة في مجلد `.venv`
- يمكن تشغيل البرنامج بدون إنترنت بعد تثبيت المكتبات

## الدعم الفني

إذا واجهت أي مشاكل، يرجى:
1. التأكد من تثبيت Python بشكل صحيح
2. تشغيل `تثبيت_المكتبات.bat`
3. استخدام `تشغيل_محسن.bat`
