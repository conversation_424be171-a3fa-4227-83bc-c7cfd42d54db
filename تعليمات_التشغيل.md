# تعليمات تشغيل نظام إدارة رواتب الموظفين - الإصدار المحدث

## التحديثات الجديدة

### ✅ التحديثات المضافة:
1. **إخفاء النافذة السوداء**: البرنامج يعمل الآن بدون إظهار نافذة الأوامر
2. **تحديث البيانات المحسن**: في تبويب سجل الموظفين، يتم الآن تحديث بيانات الموظف المحدد فقط
3. **أزرار التنقل الجديدة**:
   - زر "خروج من البرنامج" في الواجهة الرئيسية
   - أزرار "العودة للواجهة الرئيسية" في جميع التبويبات
4. **تحسين واجهة المستخدم**: ألوان وتصميم محسن للأزرار

## ملفات التشغيل المتاحة

### 1. تشغيل_محسن.bat (الموصى به) ⭐
- **الوصف**: الملف الأفضل لتشغيل البرنامج مع إخفاء النافذة السوداء
- **المميزات**:
  - يتحقق من وجود البيئة الافتراضية تلقائياً
  - يشغل النسخة الكاملة إذا كانت المكتبات متوفرة
  - يخفي النافذة السوداء تماماً
  - يعمل في الخلفية بدون إزعاج
- **الاستخدام**: انقر نقرة مزدوجة على الملف

### 2. تشغيل_آمن.bat
- **الوصف**: يشغل النسخة الكاملة من البرنامج
- **المتطلبات**: يحتاج إلى تثبيت جميع المكتبات المطلوبة
- **الاستخدام**: للمستخدمين المتقدمين

### 3. تشغيل_بسيط.bat
- **الوصف**: يشغل نسخة مبسطة من البرنامج
- **المميزات**: لا يحتاج إلى مكتبات إضافية
- **القيود**: وظائف محدودة (بدون رسوم بيانية)

### 4. تثبيت_المكتبات.bat
- **الوصف**: يثبت المكتبات المطلوبة للبرنامج
- **الاستخدام**: شغل هذا الملف أولاً إذا كانت هناك رسائل خطأ

## المميزات الجديدة في الواجهة

### 🎯 تحسينات التنقل:
- **زر الخروج**: في الواجهة الرئيسية لإغلاق البرنامج بأمان
- **أزرار العودة**: في كل تبويب للعودة السريعة للواجهة الرئيسية
- **ألوان مميزة**: أزرار ملونة لسهولة التمييز (أزرق للعودة، أحمر للخروج)

### 📊 تحسينات سجل الموظفين:
- **تحديث ذكي**: عند الضغط على "تحديث البيانات" يتم عرض بيانات الموظف المحدد فقط
- **حفظ الاختيار**: يتم الاحتفاظ باختيار القسم والموظف عند التحديث
- **أداء محسن**: تحميل أسرع للبيانات

## حل المشاكل الشائعة

### ✅ مشكلة: "ModuleNotFoundError: No module named 'matplotlib'"
**الحل**:
1. شغل ملف `تثبيت_المكتبات.bat`
2. أو استخدم `تشغيل_بسيط.bat` للنسخة البسيطة

### ✅ مشكلة: "Python غير موجود"
**الحل**:
1. شغل ملف `تثبيت_Python.bat`
2. أو حمل Python من الموقع الرسمي: https://www.python.org/downloads/

### ✅ مشكلة: النافذة السوداء تظهر وتختفي
**الحل**:
- استخدم `تشغيل_محسن.bat` الذي يخفي النافذة السوداء تماماً ✨

## ترتيب التشغيل الموصى به

1. **للمرة الأولى**: شغل `تثبيت_المكتبات.bat` (مرة واحدة فقط)
2. **للاستخدام العادي**: شغل `تشغيل_محسن.bat` ⭐ (الأفضل)
3. **في حالة المشاكل**: شغل `تشغيل_بسيط.bat`

## كيفية استخدام المميزات الجديدة

### 🔄 للعودة للواجهة الرئيسية:
- ابحث عن الزر الأزرق "العودة للواجهة الرئيسية" في أي تبويب
- اضغط عليه للانتقال فوراً للتبويب الأول

### 🚪 للخروج من البرنامج:
- في الواجهة الرئيسية، ابحث عن الزر الأحمر "خروج من البرنامج"
- اضغط عليه وأكد الخروج

### 📋 لتحديث بيانات موظف محدد:
1. اذهب لتبويب "سجل الموظفين الشهري"
2. اختر القسم من القائمة المنسدلة
3. اختر الموظف من القائمة المنسدلة
4. اضغط "تحديث البيانات" - ستظهر بيانات هذا الموظف فقط

## معلومات إضافية

- البرنامج يستخدم بيئة افتراضية (Virtual Environment) لإدارة المكتبات
- جميع المكتبات المطلوبة مثبتة في مجلد `.venv`
- يمكن تشغيل البرنامج بدون إنترنت بعد تثبيت المكتبات

## الدعم الفني

إذا واجهت أي مشاكل، يرجى:
1. التأكد من تثبيت Python بشكل صحيح
2. تشغيل `تثبيت_المكتبات.bat`
3. استخدام `تشغيل_محسن.bat`
