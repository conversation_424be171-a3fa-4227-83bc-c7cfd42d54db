#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Employee Salary Management System
Simple Main Application Entry Point - Without matplotlib dependency
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import traceback

class SimpleApplication:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة رواتب الموظفين - النسخة البسيطة")
        self.root.geometry("1000x600")
        self.root.minsize(800, 500)
        
        # Set up Arabic support
        self.root.option_add('*Font', 'Arial 10')
        
        # Try to set application icon
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"Could not set application icon: {e}")
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Add header
        ttk.Label(
            self.main_frame,
            text="نظام إدارة رواتب الموظفين",
            font=("Arial", 18, "bold")
        ).pack(pady=20)
        
        # Add status message
        ttk.Label(
            self.main_frame,
            text="تم تشغيل النسخة البسيطة من البرنامج بدون مكتبة matplotlib.",
            wraplength=600
        ).pack(pady=10)
        
        # Add instructions
        ttk.Label(
            self.main_frame,
            text="يرجى تثبيت المكتبات المطلوبة باستخدام ملف 'تثبيت_المكتبات.bat' ثم إعادة تشغيل البرنامج.",
            wraplength=600
        ).pack(pady=10)
        
        # Add buttons frame
        buttons_frame = ttk.Frame(self.main_frame)
        buttons_frame.pack(pady=20)
        
        # Add button to install dependencies
        ttk.Button(
            buttons_frame,
            text="تثبيت المكتبات المطلوبة",
            command=self.install_dependencies
        ).grid(row=0, column=0, padx=10, pady=10)
        
        # Add button to exit
        ttk.Button(
            buttons_frame,
            text="خروج",
            command=self.root.destroy
        ).grid(row=0, column=1, padx=10, pady=10)
    
    def install_dependencies(self):
        """Install required dependencies"""
        try:
            import subprocess
            
            # Create a new window to show the installation progress
            install_window = tk.Toplevel(self.root)
            install_window.title("تثبيت المكتبات المطلوبة")
            install_window.geometry("600x400")
            
            # Create a frame for the installation window
            install_frame = ttk.Frame(install_window, padding=20)
            install_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create a text widget to display installation progress
            progress_text = tk.Text(install_frame, wrap=tk.WORD, width=60, height=20)
            progress_text.pack(fill=tk.BOTH, expand=True)
            
            # Function to update the progress text
            def update_progress(message):
                progress_text.insert(tk.END, message + "\n")
                progress_text.see(tk.END)
                progress_text.update()
            
            update_progress("جاري تثبيت المكتبات المطلوبة...")
            
            # Install required packages
            packages = ["pandas", "matplotlib", "pillow"]
            for package in packages:
                update_progress(f"جاري تثبيت {package}...")
                try:
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", package],
                        capture_output=True,
                        text=True
                    )
                    update_progress(f"تم تثبيت {package} بنجاح.")
                    update_progress(result.stdout)
                except Exception as e:
                    update_progress(f"خطأ في تثبيت {package}: {str(e)}")
            
            update_progress("\nاكتمل تثبيت المكتبات. يرجى إعادة تشغيل البرنامج.")
            
            # Add a button to close the window
            ttk.Button(
                install_frame,
                text="إغلاق",
                command=install_window.destroy
            ).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror(
                "خطأ في تثبيت المكتبات",
                f"حدث خطأ أثناء محاولة تثبيت المكتبات المطلوبة:\n\n{str(e)}"
            )
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    try:
        app = SimpleApplication()
        app.run()
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"Error: {str(e)}")
        print(f"Details:\n{error_details}")
        
        # Try to show an error message box
        try:
            messagebox.showerror(
                "خطأ في تشغيل البرنامج",
                f"حدث خطأ أثناء تشغيل البرنامج:\n\n{str(e)}\n\nتفاصيل الخطأ:\n{error_details}"
            )
        except:
            pass
        
        # Log the error to a file
        with open("error_log.txt", "w", encoding="utf-8") as f:
            f.write(f"Error: {str(e)}\n\n")
            f.write(f"Details:\n{error_details}")

if __name__ == "__main__":
    main()