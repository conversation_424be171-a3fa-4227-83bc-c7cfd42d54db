# حل مشكلة تشغيل نظام إدارة رواتب الموظفين

## المشكلة

قد تواجه مشكلة في تشغيل البرنامج على بعض أجهزة الكمبيوتر، حيث يتم فتح نافذة موجه الأوامر ثم تغلق فوراً دون ظهور واجهة البرنامج.

## الحل

تم إضافة ملفات جديدة لمساعدتك في تشخيص المشكلة وتشغيل البرنامج بشكل صحيح:

1. **تشخيص_متقدم.bat**: يقوم بفحص النظام للتأكد من وجود جميع المتطلبات ويحاول تشغيل البرنامج مع عرض أي أخطاء قد تحدث.

2. **تشغيل_آمن.bat**: يقوم بتشغيل نسخة معدلة من البرنامج تتجاوز المشكلة المحتملة مع إخفاء نافذة وحدة التحكم.

## خطوات الإصلاح

1. قم بتشغيل ملف `تثبيت_Python.bat` إذا لم يكن Python مثبتاً على جهازك.

2. قم بتشغيل ملف `install_dependencies.bat` لتثبيت جميع المكتبات المطلوبة.

3. قم بتشغيل ملف `تشخيص_متقدم.bat` لتحديد المشكلة بدقة.

4. قم بتشغيل ملف `تشغيل_آمن.bat` لتشغيل البرنامج بالوضع الآمن.

## ملاحظات هامة

- إذا كان البرنامج يعمل باستخدام `تشغيل_آمن.bat`، فهذا يعني أن المشكلة كانت في مكتبة pywin32 التي تستخدم لإخفاء نافذة وحدة التحكم.

- إذا استمرت المشكلة، يرجى التواصل مع مطور البرنامج وتزويده بنتائج ملف `تشخيص_متقدم.bat`.