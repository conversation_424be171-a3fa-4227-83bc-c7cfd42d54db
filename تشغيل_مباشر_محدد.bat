@echo off
echo ===== تشغيل نظام إدارة رواتب الموظفين =====
echo.

REM تحديد مسار Python
set PYTHON_PATH=

REM التحقق من وجود Python 3.11
if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    echo تم العثور على Python 3.11
    goto run_app
)

REM التحقق من وجود Python 3.13
if exist "C:\Python313\python.exe" (
    set PYTHON_PATH=C:\Python313\python.exe
    echo تم العثور على Python 3.13
    goto run_app
)

REM إذا لم يتم العثور على Python
echo لم يتم العثور على Python بشكل صحيح.
echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
goto end

:run_app
echo.
echo جاري تشغيل البرنامج باستخدام %PYTHON_PATH%
echo.

REM التحقق من وجود الملفات الضرورية
if not exist app\main.py (
    echo ملف main.py غير موجود!
    goto end
)

REM تشغيل البرنامج
"%PYTHON_PATH%" app/main.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo التحقق من المكتبات المطلوبة...
    "%PYTHON_PATH%" -c "import tkinter; print('tkinter موجود')" 2>nul
    if %ERRORLEVEL% NEQ 0 echo tkinter غير موجود
    "%PYTHON_PATH%" -c "import pandas; print('pandas موجود')" 2>nul
    if %ERRORLEVEL% NEQ 0 echo pandas غير موجود
    "%PYTHON_PATH%" -c "import matplotlib; print('matplotlib موجود')" 2>nul
    if %ERRORLEVEL% NEQ 0 echo matplotlib غير موجود
    echo.
    echo يرجى تشغيل ملف تثبيت_المكتبات.bat لتثبيت المكتبات المطلوبة
)

:end
pause