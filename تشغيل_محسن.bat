@echo off

REM الانتقال إلى مجلد البرنامج
cd /d "%~dp0"

REM التحقق من وجود البيئة الافتراضية
if exist ".venv\Scripts\python.exe" (
    REM تشغيل البرنامج باستخدام البيئة الافتراضية مع إخفاء النافذة
    start "" /min .venv\Scripts\pythonw.exe app\main_fixed.py

) else (
    REM البحث عن Python في المسارات المختلفة
    set PYTHON_PATH=

    REM التحقق من وجود Python 3.11
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\pythonw.exe" (
        start "" /min "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\pythonw.exe" app\simple_main.py
        goto end
    )

    REM التحقق من وجود Python 3.13
    if exist "C:\Python313\pythonw.exe" (
        start "" /min "C:\Python313\pythonw.exe" app\simple_main.py
        goto end
    )

    REM محاولة استخدام pythonw من PATH
    pythonw --version >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        start "" /min pythonw app\simple_main.py
        goto end
    )

    REM إذا لم يتم العثور على Python، استخدم النسخة العادية
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
        start "" /min "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" app\simple_main.py
        goto end
    )

    if exist "C:\Python313\python.exe" (
        start "" /min "C:\Python313\python.exe" app\simple_main.py
        goto end
    )

    python --version >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        start "" /min python app\simple_main.py
        goto end
    )

    REM إذا لم يتم العثور على Python نهائياً
    echo لم يتم العثور على Python في النظام
    echo يرجى تثبيت Python أولاً أو تشغيل ملف تثبيت_Python.bat
    pause
)

:end
