@echo off
echo ===== نظام إدارة رواتب الموظفين - الإصدار المحسن =====
echo.

REM الانتقال إلى مجلد البرنامج
cd /d "%~dp0"

REM التحقق من وجود البيئة الافتراضية
if exist ".venv\Scripts\python.exe" (
    echo تم العثور على البيئة الافتراضية...
    echo تشغيل البرنامج الكامل مع جميع المكتبات...
    echo.
    
    REM تشغيل البرنامج باستخدام البيئة الافتراضية
    .venv\Scripts\python.exe app\main_fixed.py
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo حدث خطأ أثناء تشغيل البرنامج الكامل
        echo جاري المحاولة مع النسخة البسيطة...
        echo.
        .venv\Scripts\python.exe app\simple_main.py
    )
) else (
    echo لم يتم العثور على البيئة الافتراضية
    echo جاري البحث عن Python في النظام...
    echo.
    
    REM البحث عن Python في المسارات المختلفة
    set PYTHON_PATH=
    
    REM التحقق من وجود Python 3.11
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
        set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
        echo تم العثور على Python 3.11
        goto run_simple
    )
    
    REM التحقق من وجود Python 3.13
    if exist "C:\Python313\python.exe" (
        set PYTHON_PATH=C:\Python313\python.exe
        echo تم العثور على Python 3.13
        goto run_simple
    )
    
    REM محاولة استخدام python من PATH
    python --version >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        set PYTHON_PATH=python
        echo تم العثور على Python في النظام
        goto run_simple
    )
    
    REM إذا لم يتم العثور على Python
    echo لم يتم العثور على Python في النظام
    echo يرجى تثبيت Python أولاً أو تشغيل ملف تثبيت_Python.bat
    goto end
    
    :run_simple
    echo تشغيل النسخة البسيطة من البرنامج...
    echo.
    "%PYTHON_PATH%" app\simple_main.py
)

:end
echo.
echo شكراً لاستخدام نظام إدارة رواتب الموظفين
pause
