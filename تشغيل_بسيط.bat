@echo off
echo ===== تشغيل النسخة البسيطة من نظام إدارة رواتب الموظفين =====
echo.

REM تحديد مسار Python
set PYTHON_PATH=

REM التحقق من وجود Python 3.11
if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    echo تم العثور على Python 3.11
    goto run_app
)

REM التحقق من وجود Python 3.13
if exist "C:\Python313\python.exe" (
    set PYTHON_PATH=C:\Python313\python.exe
    echo تم العثور على Python 3.13
    goto run_app
)

REM إذا لم يتم العثور على Python
echo لم يتم العثور على Python بشكل صحيح.
echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
goto end

:run_app
echo.
echo جاري تشغيل النسخة البسيطة من البرنامج باستخدام %PYTHON_PATH%
echo.

REM تفعيل البيئة الافتراضية إذا كانت موجودة
if exist ".venv\Scripts\activate.bat" (
    echo تفعيل البيئة الافتراضية...
    call .venv\Scripts\activate.bat
    "%PYTHON_PATH%" app/simple_main.py
) else (
    echo تشغيل البرنامج بدون البيئة الافتراضية...
    "%PYTHON_PATH%" app/simple_main.py
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo التحقق من المكتبات الأساسية...
    "%PYTHON_PATH%" -c "import tkinter; print('tkinter موجود')" 2>nul
    if %ERRORLEVEL% NEQ 0 echo tkinter غير موجود
)

:end
pause