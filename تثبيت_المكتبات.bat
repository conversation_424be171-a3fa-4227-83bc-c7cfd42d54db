@echo off
echo ===== تثبيت المكتبات المطلوبة لنظام إدارة رواتب الموظفين =====
echo.

REM تحديد مسار Python
set PYTHON_PATH=

REM التحقق من وجود Python 3.11
if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    echo تم العثور على Python 3.11
    goto install_packages
)

REM التحقق من وجود Python 3.13
if exist "C:\Python313\python.exe" (
    set PYTHON_PATH=C:\Python313\python.exe
    echo تم العثور على Python 3.13
    goto install_packages
)

REM إذا لم يتم العثور على Python
echo لم يتم العثور على Python بشكل صحيح.
echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
goto end

:install_packages
echo.
echo جاري تثبيت المكتبات المطلوبة باستخدام %PYTHON_PATH%
echo.

echo تحديث pip...
"%PYTHON_PATH%" -m pip install --upgrade pip

echo.
echo تثبيت matplotlib...
"%PYTHON_PATH%" -m pip install matplotlib

echo.
echo تثبيت pandas...
"%PYTHON_PATH%" -m pip install pandas

echo.
echo تثبيت pillow...
"%PYTHON_PATH%" -m pip install pillow

echo.
echo تثبيت pywin32...
"%PYTHON_PATH%" -m pip install pywin32

echo.
echo تم تثبيت جميع المكتبات المطلوبة بنجاح!
echo يمكنك الآن تشغيل البرنامج باستخدام ملف run_app.bat

:end
pause